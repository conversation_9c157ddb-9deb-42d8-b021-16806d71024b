'use client';

import { useState, useEffect } from 'react';
// import { useRouter } from 'next/navigation'; // No longer needed after removing client-side redirect
import Link from 'next/link';
import AdminLayout from '@/layouts/AdminLayout';
import { useAuth } from '@/hooks/useAuth';

export default function AdminDashboard() {
  // const router = useRouter(); // No longer needed
  const { user, loading: authLoading } = useAuth();
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalBusinesses: 0,
    totalBookings: 0,
    totalReviews: 0,
    pendingKYC: 0,
    pendingKYB: 0,
    totalSubscriptions: 0,
    totalAds: 0,
    totalNewsletterSubscribers: 0,
    totalRevenue: 0
  });
  const [recentUsers, setRecentUsers] = useState([]);
  const [recentBusinesses, setRecentBusinesses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Redirect logic removed - Middleware should handle unauthorized access.
  // The check before rendering (lines 118-120) provides a fallback.

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!user) return;

      setLoading(true);
      setError(null);

      try {
        // For now, we'll use mock data
        // In a real implementation, you would fetch this data from your API

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800));

        // Mock stats data
        setStats({
          totalUsers: 256,
          totalBusinesses: 124,
          totalBookings: 89,
          totalReviews: 312,
          pendingKYC: 8,
          pendingKYB: 15,
          totalSubscriptions: 78,
          totalAds: 42,
          totalNewsletterSubscribers: 1250,
          totalRevenue: 12450.75
        });

        // Mock recent users data
        setRecentUsers([
          { id: 'u1', name: 'John Doe', email: '<EMAIL>', role: 'user', createdAt: '2023-06-15T10:30:00Z' },
          { id: 'u2', name: 'Jane Smith', email: '<EMAIL>', role: 'business_owner', createdAt: '2023-06-14T14:45:00Z' },
          { id: 'u3', name: 'Robert Johnson', email: '<EMAIL>', role: 'user', createdAt: '2023-06-13T09:15:00Z' },
          { id: 'u4', name: 'Emily Davis', email: '<EMAIL>', role: 'business_owner', createdAt: '2023-06-12T16:20:00Z' },
          { id: 'u5', name: 'Michael Wilson', email: '<EMAIL>', role: 'user', createdAt: '2023-06-11T11:10:00Z' }
        ]);

        // Mock recent businesses data
        setRecentBusinesses([
          { id: 'b1', name: 'Cafe Delight', category: 'Restaurant', owner: 'Jane Smith', kybVerified: 'verified', createdAt: '2023-06-15T08:30:00Z' },
          { id: 'b2', name: 'Tech Solutions', category: 'Technology', owner: 'Robert Brown', kybVerified: 'pending', createdAt: '2023-06-14T13:45:00Z' },
          { id: 'b3', name: 'Fitness Hub', category: 'Gym', owner: 'Emily Davis', kybVerified: 'unverified', createdAt: '2023-06-13T10:15:00Z' },
          { id: 'b4', name: 'Green Gardens', category: 'Landscaping', owner: 'Michael Wilson', kybVerified: 'verified', createdAt: '2023-06-12T15:20:00Z' },
          { id: 'b5', name: 'City Hotel', category: 'Accommodation', owner: 'Sarah Johnson', kybVerified: 'pending', createdAt: '2023-06-11T09:10:00Z' }
        ]);

      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError('Failed to load dashboard data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (user && (user.role === 'admin' || user.role === 'super_admin')) {
      fetchDashboardData();
    }
  }, [user]);

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  if (authLoading || loading) {
    return (
      <AdminLayout>
        <div className="text-center py-5">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Loading dashboard data...</p>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      </AdminLayout>
    );
  }

  // If user is not logged in or not an admin, show nothing (will redirect)
  if (!user || (user.role !== 'admin' && user.role !== 'super_admin')) {
    return null;
  }

  return (
    <AdminLayout>
      <div className="admin-dashboard">
        <div className="dashboard-header mb-4">
          <h1 className="dashboard-title">Dashboard</h1>
          <p className="dashboard-subtitle">Welcome back, {user.name}!</p>
          {user.username && (
            <p className="dashboard-username">@{user.username}</p>
          )}
        </div>

        {/* Stats Cards */}
        <div className="row mb-4">
          {/* User Stats */}
          <div className="col-xl-3 col-md-6 mb-4">
            <div className="card border-left-primary shadow h-100 py-2">
              <div className="card-body">
                <div className="row no-gutters align-items-center">
                  <div className="col mr-2">
                    <div className="text-xs font-weight-bold text-primary text-uppercase mb-1">
                      Total Users
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">{stats.totalUsers}</div>
                  </div>
                  <div className="col-auto">
                    <i className="fas fa-users fa-2x text-gray-300"></i>
                  </div>
                </div>
                <div className="mt-2">
                  <Link href="/admin/manage-users" className="btn btn-sm btn-outline-primary w-100">
                    Manage Users
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Business Stats */}
          <div className="col-xl-3 col-md-6 mb-4">
            <div className="card border-left-success shadow h-100 py-2">
              <div className="card-body">
                <div className="row no-gutters align-items-center">
                  <div className="col mr-2">
                    <div className="text-xs font-weight-bold text-success text-uppercase mb-1">
                      Total Businesses
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">{stats.totalBusinesses}</div>
                  </div>
                  <div className="col-auto">
                    <i className="fas fa-building fa-2x text-gray-300"></i>
                  </div>
                </div>
                <div className="mt-2">
                  <Link href="/admin/manage-businesses" className="btn btn-sm btn-outline-success w-100">
                    Manage Businesses
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Booking Stats */}
          <div className="col-xl-3 col-md-6 mb-4">
            <div className="card border-left-info shadow h-100 py-2">
              <div className="card-body">
                <div className="row no-gutters align-items-center">
                  <div className="col mr-2">
                    <div className="text-xs font-weight-bold text-info text-uppercase mb-1">
                      Total Bookings
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">{stats.totalBookings}</div>
                  </div>
                  <div className="col-auto">
                    <i className="fas fa-calendar-check fa-2x text-gray-300"></i>
                  </div>
                </div>
                <div className="mt-2">
                  <Link href="/admin/manage-bookings" className="btn btn-sm btn-outline-info w-100">
                    Manage Bookings
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Review Stats */}
          <div className="col-xl-3 col-md-6 mb-4">
            <div className="card border-left-danger shadow h-100 py-2">
              <div className="card-body">
                <div className="row no-gutters align-items-center">
                  <div className="col mr-2">
                    <div className="text-xs font-weight-bold text-danger text-uppercase mb-1">
                      Total Reviews
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">{stats.totalReviews}</div>
                  </div>
                  <div className="col-auto">
                    <i className="fas fa-star fa-2x text-gray-300"></i>
                  </div>
                </div>
                <div className="mt-2">
                  <Link href="/admin/moderate-reviews" className="btn btn-sm btn-outline-danger w-100">
                    Moderate Reviews
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Second Row of Stats */}
        <div className="row mb-4">
          {/* KYC Stats */}
          <div className="col-xl-3 col-md-6 mb-4">
            <div className="card border-left-warning shadow h-100 py-2">
              <div className="card-body">
                <div className="row no-gutters align-items-center">
                  <div className="col mr-2">
                    <div className="text-xs font-weight-bold text-warning text-uppercase mb-1">
                      Pending KYC
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">{stats.pendingKYC}</div>
                  </div>
                  <div className="col-auto">
                    <i className="fas fa-user-check fa-2x text-gray-300"></i>
                  </div>
                </div>
                <div className="mt-2">
                  <Link href="/admin/review-kyc" className="btn btn-sm btn-outline-warning w-100">
                    Review KYC
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* KYB Stats */}
          <div className="col-xl-3 col-md-6 mb-4">
            <div className="card border-left-warning shadow h-100 py-2">
              <div className="card-body">
                <div className="row no-gutters align-items-center">
                  <div className="col mr-2">
                    <div className="text-xs font-weight-bold text-warning text-uppercase mb-1">
                      Pending KYB
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">{stats.pendingKYB}</div>
                  </div>
                  <div className="col-auto">
                    <i className="fas fa-building-shield fa-2x text-gray-300"></i>
                  </div>
                </div>
                <div className="mt-2">
                  <Link href="/admin/review-kyb" className="btn btn-sm btn-outline-warning w-100">
                    Review KYB
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Subscription Stats */}
          <div className="col-xl-3 col-md-6 mb-4">
            <div className="card border-left-success shadow h-100 py-2">
              <div className="card-body">
                <div className="row no-gutters align-items-center">
                  <div className="col mr-2">
                    <div className="text-xs font-weight-bold text-success text-uppercase mb-1">
                      Subscriptions
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">{stats.totalSubscriptions}</div>
                  </div>
                  <div className="col-auto">
                    <i className="fas fa-tags fa-2x text-gray-300"></i>
                  </div>
                </div>
                <div className="mt-2">
                  <Link href="/admin/manage-subscription-plans" className="btn btn-sm btn-outline-success w-100">
                    Manage Plans
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Revenue Stats */}
          <div className="col-xl-3 col-md-6 mb-4">
            <div className="card border-left-primary shadow h-100 py-2">
              <div className="card-body">
                <div className="row no-gutters align-items-center">
                  <div className="col mr-2">
                    <div className="text-xs font-weight-bold text-primary text-uppercase mb-1">
                      Total Revenue
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">${stats.totalRevenue.toFixed(2)}</div>
                  </div>
                  <div className="col-auto">
                    <i className="fas fa-dollar-sign fa-2x text-gray-300"></i>
                  </div>
                </div>
                <div className="mt-2">
                  <Link href="/admin/payments" className="btn btn-sm btn-outline-primary w-100">
                    View Payments
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Third Row of Stats */}
        <div className="row mb-4">
          {/* Ads Stats */}
          <div className="col-xl-4 col-md-6 mb-4">
            <div className="card border-left-info shadow h-100 py-2">
              <div className="card-body">
                <div className="row no-gutters align-items-center">
                  <div className="col mr-2">
                    <div className="text-xs font-weight-bold text-info text-uppercase mb-1">
                      Active Advertisements
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">{stats.totalAds}</div>
                  </div>
                  <div className="col-auto">
                    <i className="fas fa-ad fa-2x text-gray-300"></i>
                  </div>
                </div>
                <div className="mt-2">
                  <Link href="/admin/manage-advertisements" className="btn btn-sm btn-outline-info w-100">
                    Manage Ads
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Newsletter Stats */}
          <div className="col-xl-4 col-md-6 mb-4">
            <div className="card border-left-success shadow h-100 py-2">
              <div className="card-body">
                <div className="row no-gutters align-items-center">
                  <div className="col mr-2">
                    <div className="text-xs font-weight-bold text-success text-uppercase mb-1">
                      Newsletter Subscribers
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">{stats.totalNewsletterSubscribers}</div>
                  </div>
                  <div className="col-auto">
                    <i className="fas fa-envelope fa-2x text-gray-300"></i>
                  </div>
                </div>
                <div className="mt-2 d-flex">
                  <Link href="/admin/manage-newsletter" className="btn btn-sm btn-outline-success flex-grow-1 me-1">
                    Subscribers
                  </Link>
                  <Link href="/admin/create-newsletter" className="btn btn-sm btn-outline-success flex-grow-1">
                    Create
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Analytics Stats */}
          <div className="col-xl-4 col-md-6 mb-4">
            <div className="card border-left-primary shadow h-100 py-2">
              <div className="card-body">
                <div className="row no-gutters align-items-center">
                  <div className="col mr-2">
                    <div className="text-xs font-weight-bold text-primary text-uppercase mb-1">
                      Platform Analytics
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">
                      <i className="fas fa-chart-line me-2"></i> View Reports
                    </div>
                  </div>
                  <div className="col-auto">
                    <i className="fas fa-chart-bar fa-2x text-gray-300"></i>
                  </div>
                </div>
                <div className="mt-2">
                  <Link href="/admin/analytics" className="btn btn-sm btn-outline-primary w-100">
                    View Analytics
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Data */}
        <div className="row">
          {/* Recent Users */}
          <div className="col-xl-6 col-lg-6 mb-4">
            <div className="card shadow mb-4">
              <div className="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 className="m-0 font-weight-bold text-primary">Recent Users</h6>
                <Link href="/admin/users" className="btn btn-sm btn-primary">
                  View All
                </Link>
              </div>
              <div className="card-body">
                <div className="table-responsive">
                  <table className="table table-bordered" width="100%" cellSpacing="0">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Joined</th>
                      </tr>
                    </thead>
                    <tbody>
                      {recentUsers.map((user) => (
                        <tr key={user.id}>
                          <td>{user.name}</td>
                          <td>{user.email}</td>
                          <td>
                            <span className={`badge ${
                              user.role === 'admin' ? 'bg-danger' :
                              user.role === 'business_owner' ? 'bg-primary' :
                              'bg-secondary'
                            }`}>
                              {user.role === 'business_owner' ? 'Business Owner' :
                               user.role === 'admin' ? 'Admin' :
                               'User'}
                            </span>
                          </td>
                          <td>{formatDate(user.createdAt)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Businesses */}
          <div className="col-xl-6 col-lg-6 mb-4">
            <div className="card shadow mb-4">
              <div className="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 className="m-0 font-weight-bold text-primary">Recent Businesses</h6>
                <Link href="/admin/businesses" className="btn btn-sm btn-primary">
                  View All
                </Link>
              </div>
              <div className="card-body">
                <div className="table-responsive">
                  <table className="table table-bordered" width="100%" cellSpacing="0">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Category</th>
                        <th>Status</th>
                        <th>Created</th>
                      </tr>
                    </thead>
                    <tbody>
                      {recentBusinesses.map((business) => (
                        <tr key={business.id}>
                          <td>{business.name}</td>
                          <td>{business.category}</td>
                          <td>
                            <span className={`badge ${
                              business.kybVerified === 'verified' ? 'bg-success' :
                              business.kybVerified === 'rejected' ? 'bg-danger' :
                              business.kybVerified === 'pending' ? 'bg-warning text-dark' :
                              'bg-secondary'
                            }`}>
                              {business.kybVerified === 'verified' ? 'Verified' :
                               business.kybVerified === 'rejected' ? 'Rejected' :
                               business.kybVerified === 'pending' ? 'Pending' :
                               'Unverified'}
                            </span>
                          </td>
                          <td>{formatDate(business.createdAt)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .dashboard-title {
          font-size: 1.75rem;
          font-weight: 600;
          margin-bottom: 0.5rem;
        }

        .dashboard-subtitle {
          color: #6c757d;
          margin-bottom: 0.25rem;
        }

        .dashboard-username {
          color: #6c757d;
          font-size: 0.9rem;
          font-style: italic;
          margin-bottom: 1.5rem;
        }

        .card {
          border: none;
          border-radius: 0.35rem;
          box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }

        .card-header {
          background-color: #f8f9fc;
          border-bottom: 1px solid #e3e6f0;
        }

        .border-left-primary {
          border-left: 0.25rem solid #4e73df !important;
        }

        .border-left-success {
          border-left: 0.25rem solid #1cc88a !important;
        }

        .border-left-info {
          border-left: 0.25rem solid #36b9cc !important;
        }

        .border-left-warning {
          border-left: 0.25rem solid #f6c23e !important;
        }

        .border-left-danger {
          border-left: 0.25rem solid #e74a3b !important;
        }

        .text-xs {
          font-size: 0.7rem;
        }

        .text-gray-300 {
          color: #dddfeb !important;
        }

        .text-gray-800 {
          color: #5a5c69 !important;
        }
      `}</style>
    </AdminLayout>
  );
}
