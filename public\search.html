<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Results - Finda</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- Line Icons -->
    <link href="https://cdn.lineicons.com/3.0/lineicons.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/styles.css">
</head>
<body>
    <!-- Header Container -->
    <div id="header-container"></div>

    <!-- Main Content -->
    <main>
        <!-- Search Results Header -->
        <section class="py-4 bg-light">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h1 class="search-title">Search Results</h1>
                        <div id="search-bar-container" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Search Results -->
        <section class="py-5">
            <div class="container">
                <!-- Map View Toggle -->
                <div class="d-flex justify-content-end mb-4">
                    <div class="btn-group" role="group" aria-label="View Toggle">
                        <button type="button" class="btn btn-outline-primary active" id="list-view-btn">List View</button>
                        <button type="button" class="btn btn-outline-primary" id="map-view-btn">Map View</button>
                    </div>
                </div>

                <!-- Map Container (hidden by default) -->
                <div id="map-container" class="mb-4" style="height: 400px; display: none;"></div>

                <div class="row">
                    <!-- Filters Sidebar -->
                    <div class="col-lg-3 col-md-4">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Filters</h5>
                            </div>
                            <div class="card-body">
                                <form id="filter-form">
                                    <div class="mb-3">
                                        <label class="form-label">Rating</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="4" id="rating4">
                                            <label class="form-check-label" for="rating4">
                                                4+ Stars
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="3" id="rating3">
                                            <label class="form-check-label" for="rating3">
                                                3+ Stars
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Price Range</label>
                                        <select class="form-select" id="priceRange">
                                            <option value="">Any</option>
                                            <option value="1">$</option>
                                            <option value="2">$$</option>
                                            <option value="3">$$$</option>
                                            <option value="4">$$$$</option>
                                        </select>
                                    </div>

                                    <button type="submit" class="btn btn-primary w-100">Apply Filters</button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Results -->
                    <div class="col-lg-9 col-md-8">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <div id="results-count">
                                <!-- Will be populated by JavaScript -->
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    Sort By
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="sortDropdown">
                                    <li><a class="dropdown-item" href="#" data-sort="relevance">Relevance</a></li>
                                    <li><a class="dropdown-item" href="#" data-sort="rating">Rating</a></li>
                                    <li><a class="dropdown-item" href="#" data-sort="reviews">Most Reviews</a></li>
                                </ul>
                            </div>
                        </div>

                        <!-- Business Listings Container -->
                        <div id="business-listings-container" class="row"></div>

                        <!-- Pagination -->
                        <nav aria-label="Page navigation" class="mt-4">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- Will be populated by JavaScript -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer Container -->
    <div id="footer-container"></div>

    <!-- Login Modal -->
    <div class="modal fade" id="login" tabindex="-1" aria-labelledby="loginLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="loginLabel">Login</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="login-form">
                        <div class="mb-3">
                            <label for="email" class="form-label">Email address</label>
                            <input type="email" class="form-control" id="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" required>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember">
                            <label class="form-check-label" for="remember">Remember me</label>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">Login</button>
                    </form>
                    <div class="mt-3 text-center">
                        <p>Don't have an account? <a href="register.html">Register</a></p>
                        <p><a href="forgot-password.html">Forgot Password?</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Google Maps API -->
    <script src="https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&libraries=places" defer></script>

    <!-- MarkerClusterer for Google Maps -->
    <script src="https://unpkg.com/@googlemaps/markerclusterer/dist/index.min.js"></script>

    <!-- Include Header Script -->
    <script src="assets/js/include-header.js"></script>

    <!-- Include Footer Script -->
    <script src="assets/js/include-footer.js"></script>

    <!-- Business Card Component -->
    <script type="module" src="assets/js/components/business-card.js"></script>

    <!-- Search Bar Component -->
    <script type="module" src="assets/js/components/search-bar.js"></script>

    <!-- Search Page Script -->
    <script type="module">
        import { fetchAndDisplayBusinesses } from './assets/js/components/business-card.js';

        // Map variables
        let map;
        let markers = [];
        let markerCluster;

        // Initialize Google Map
        function initMap(businesses) {
            // Default center (can be adjusted based on search results)
            const defaultCenter = { lat: 40.7128, lng: -74.0060 }; // New York City

            // Create the map
            map = new google.maps.Map(document.getElementById('map-container'), {
                zoom: 12,
                center: defaultCenter,
                mapTypeControl: true,
                streetViewControl: false,
                fullscreenControl: true
            });

            // Add markers for each business
            addMarkers(businesses);
        }

        // Add markers to the map
        function addMarkers(businesses) {
            // Clear existing markers
            clearMarkers();

            // If no businesses, return
            if (!businesses || businesses.length === 0) return;

            // Bounds to fit all markers
            const bounds = new google.maps.LatLngBounds();

            // Add a marker for each business
            businesses.forEach(business => {
                // Skip if no coordinates
                if (!business.latitude || !business.longitude) return;

                const position = {
                    lat: parseFloat(business.latitude),
                    lng: parseFloat(business.longitude)
                };

                // Create marker
                const marker = new google.maps.Marker({
                    position: position,
                    map: map,
                    title: business.name,
                    // You can add custom icons based on category
                    // icon: getCategoryIcon(business.category)
                });

                // Create info window content
                const infoContent = `
                    <div class="map-info-window">
                        <h5>${business.name}</h5>
                        <p>${business.address}, ${business.city}, ${business.state}</p>
                        ${business.averageRating ? `<p>Rating: ${business.averageRating.toFixed(1)}/5</p>` : ''}
                        <a href="business-detail.html?id=${business._id}" class="btn btn-sm btn-primary">View Details</a>
                    </div>
                `;

                // Create info window
                const infoWindow = new google.maps.InfoWindow({
                    content: infoContent
                });

                // Add click listener to marker
                marker.addListener('click', () => {
                    // Close all open info windows
                    markers.forEach(m => m.infoWindow.close());

                    // Open this info window
                    infoWindow.open(map, marker);
                });

                // Store marker and info window
                marker.infoWindow = infoWindow;
                markers.push(marker);

                // Extend bounds to include this marker
                bounds.extend(position);
            });

            // Fit map to bounds if we have markers
            if (markers.length > 0) {
                map.fitBounds(bounds);

                // If only one marker, zoom out a bit
                if (markers.length === 1) {
                    map.setZoom(15);
                }
            }

            // Add marker clustering if we have multiple markers
            if (markers.length > 1 && typeof MarkerClusterer !== 'undefined') {
                markerCluster = new MarkerClusterer(map, markers, {
                    imagePath: 'https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m'
                });
            }
        }

        // Clear all markers from the map
        function clearMarkers() {
            markers.forEach(marker => marker.setMap(null));
            markers = [];

            if (markerCluster) {
                markerCluster.clearMarkers();
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            // Get search parameters from URL
            const urlParams = new URLSearchParams(window.location.search);
            const queryParams = {};

            // Convert URL parameters to an object
            for (const [key, value] of urlParams.entries()) {
                queryParams[key] = value;
            }

            // Update search title
            const searchTitle = document.querySelector('.search-title');
            if (searchTitle) {
                let titleText = 'Search Results';
                if (queryParams.query) {
                    titleText = `Results for "${queryParams.query}"`;
                } else if (queryParams.category) {
                    titleText = `${queryParams.category} in ${queryParams.location || 'All Locations'}`;
                }
                searchTitle.textContent = titleText;
            }

            // Fetch and display businesses
            fetchAndDisplayBusinesses('#business-listings-container', queryParams)
                .then(data => {
                    // Initialize map with business data
                    if (data && data.businesses) {
                        initMap(data.businesses);
                    }
                });

            // Handle filter form submission
            const filterForm = document.getElementById('filter-form');
            if (filterForm) {
                filterForm.addEventListener('submit', (e) => {
                    e.preventDefault();

                    // Get filter values
                    const rating4 = document.getElementById('rating4').checked;
                    const rating3 = document.getElementById('rating3').checked;
                    const priceRange = document.getElementById('priceRange').value;

                    // Add filters to query params
                    if (rating4) {
                        queryParams.minRating = '4';
                    } else if (rating3) {
                        queryParams.minRating = '3';
                    } else {
                        delete queryParams.minRating;
                    }

                    if (priceRange) {
                        queryParams.priceRange = priceRange;
                    } else {
                        delete queryParams.priceRange;
                    }

                    // Fetch and display businesses with updated filters
                    fetchAndDisplayBusinesses('#business-listings-container', queryParams)
                        .then(data => {
                            // Update map markers
                            if (data && data.businesses) {
                                addMarkers(data.businesses);
                            }
                        });
                });
            }

            // Handle sort dropdown
            const sortLinks = document.querySelectorAll('[data-sort]');
            sortLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    const sortBy = e.target.getAttribute('data-sort');
                    queryParams.sort = sortBy;

                    // Update dropdown button text
                    document.getElementById('sortDropdown').textContent = `Sort By: ${sortBy.charAt(0).toUpperCase() + sortBy.slice(1)}`;

                    // Fetch and display businesses with updated sort
                    fetchAndDisplayBusinesses('#business-listings-container', queryParams)
                        .then(data => {
                            // Update map markers
                            if (data && data.businesses) {
                                addMarkers(data.businesses);
                            }
                        });
                });
            });

            // Handle view toggle
            const listViewBtn = document.getElementById('list-view-btn');
            const mapViewBtn = document.getElementById('map-view-btn');
            const mapContainer = document.getElementById('map-container');
            const resultsContainer = document.querySelector('.col-lg-9.col-md-8');
            const filtersContainer = document.querySelector('.col-lg-3.col-md-4');

            if (listViewBtn && mapViewBtn && mapContainer && resultsContainer) {
                listViewBtn.addEventListener('click', () => {
                    // Switch to list view
                    listViewBtn.classList.add('active');
                    mapViewBtn.classList.remove('active');
                    mapContainer.style.display = 'none';
                    resultsContainer.style.display = 'block';
                    filtersContainer.style.display = 'block';
                });

                mapViewBtn.addEventListener('click', () => {
                    // Switch to map view
                    mapViewBtn.classList.add('active');
                    listViewBtn.classList.remove('active');
                    mapContainer.style.display = 'block';
                    resultsContainer.style.display = 'none';
                    filtersContainer.style.display = 'none';

                    // Trigger resize event to fix map display issues
                    window.dispatchEvent(new Event('resize'));
                });
            }
        });
    </script>
</body>
</html>
