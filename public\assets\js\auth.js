// assets/js/auth.js

/**
 * Checks if a user is currently authenticated
 * @param {Function} callback - Function to call with the user object (or null if not authenticated)
 */
async function checkAuth(callback) {
    const token = localStorage.getItem('token');
    if (token) {
        try {
            const response = await fetch('/api/auth/me', { // Use the correct API endpoint
                headers: {
                    'Authorization': `Bear<PERSON> ${token}`
                }
            });

            if (response.ok) {
                const user = await response.json();
                // Store User data:
                localStorage.setItem('user', JSON.stringify(user));
                callback(user);  // Pass the user object to the callback
            } else {
                // Token is invalid or expired
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                callback(null); // No user
            }
        } catch (error) {
            console.error('Auth check error:', error);
            callback(null); // Treat fetch errors as not authenticated
        }
    } else {
        callback(null); // No token, user is not logged in
    }
}

/**
 * Logs out the current user
 */
async function handleLogout() {
    try {
        const response = await fetch('/api/auth/logout', {
            method: 'POST', // Or GET, depending on your API
            headers: {
                'Content-Type': 'application/json',
                // Include authorization header if needed
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Logout failed');
        }

        // Remove token and user data from local storage
        localStorage.removeItem('token');
        localStorage.removeItem('user');

        // Redirect to login page
        window.location.href = 'index.html'; // Adjust path if necessary

    } catch (error) {
        console.error('Logout error:', error);
        alert('Logout failed: ' + error.message);
        
        // Even if the server-side logout fails, clear local storage
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        
        // Redirect anyway to ensure the user is logged out client-side
        window.location.href = 'index.html';
    }
}

// Export the functions to be used in other modules
export { checkAuth, handleLogout as logout };
