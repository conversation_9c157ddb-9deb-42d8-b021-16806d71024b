/**
 * This script generates map marker images for different business categories.
 * Run this script with Node.js to create the marker images.
 * 
 * Usage: node scripts/generate-map-markers.js
 */

const fs = require('fs');
const path = require('path');

// Define the categories and their colors
const categories = [
  { name: 'default', color: '#4285F4', icon: 'map-marker-alt' },
  { name: 'restaurant', color: '#FF5722', icon: 'utensils' },
  { name: 'hotel', color: '#8BC34A', icon: 'hotel' },
  { name: 'shopping', color: '#9C27B0', icon: 'shopping-bag' },
  { name: 'car', color: '#2196F3', icon: 'car' },
  { name: 'hospital', color: '#F44336', icon: 'hospital' },
  { name: 'school', color: '#FF9800', icon: 'graduation-cap' },
  { name: 'gym', color: '#00BCD4', icon: 'dumbbell' },
  { name: 'beauty', color: '#E91E63', icon: 'spa' },
  { name: 'realestate', color: '#4CAF50', icon: 'home' },
  { name: 'tech', color: '#607D8B', icon: 'laptop' },
  { name: 'event', color: '#FFC107', icon: 'calendar-alt' },
  { name: 'job', color: '#3F51B5', icon: 'briefcase' }
];

// Create the directory if it doesn't exist
const markerDir = path.join(__dirname, '..', 'public', 'assets', 'img', 'map-markers');
if (!fs.existsSync(markerDir)) {
  fs.mkdirSync(markerDir, { recursive: true });
}

// Generate SVG marker for each category
categories.forEach(category => {
  const svg = `<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
  <circle cx="16" cy="16" r="14" fill="${category.color}" stroke="white" stroke-width="2" />
  <text x="16" y="16" font-family="FontAwesome" font-size="14" fill="white" text-anchor="middle" dominant-baseline="central">&#x${getIconCode(category.icon)};</text>
</svg>`;

  fs.writeFileSync(path.join(markerDir, `${category.name}.svg`), svg);
  console.log(`Generated marker for ${category.name}`);
});

console.log('All markers generated successfully!');

// Helper function to get FontAwesome icon code
function getIconCode(iconName) {
  const iconCodes = {
    'map-marker-alt': 'f3c5',
    'utensils': 'f2e7',
    'hotel': 'f594',
    'shopping-bag': 'f290',
    'car': 'f1b9',
    'hospital': 'f0f8',
    'graduation-cap': 'f19d',
    'dumbbell': 'f44b',
    'spa': 'f5bb',
    'home': 'f015',
    'laptop': 'f109',
    'calendar-alt': 'f073',
    'briefcase': 'f0b1'
  };
  
  return iconCodes[iconName] || 'f3c5'; // Default to map-marker-alt
}
