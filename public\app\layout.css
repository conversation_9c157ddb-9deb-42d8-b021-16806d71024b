/* Layout styles */
:root {
  --header-height: 60px; /* Reduced to match sticky header height */
  --header-height-scrolled: 60px;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

/* Global main content padding to prevent header overlap */
main {
  padding-top: var(--header-height);
}

/* Ensure all direct children of main have proper spacing */
main > div:first-child,
main > section:first-child {
  padding-top: 10px; /* Minimal padding for content */
}

/* Special handling for home page which has a hero section */
.home-page main > div:first-child,
.home-page main > section:first-child {
  padding-top: 0; /* No padding needed for home page hero */
}

/* Responsive adjustments */
@media (max-width: 991px) {
  :root {
    --header-height: 55px; /* Reduced to match sticky header on tablets */
    --header-height-scrolled: 55px;
  }

  main > div:first-child,
  main > section:first-child {
    padding-top: 8px; /* Minimal padding */
  }
}

@media (max-width: 768px) {
  :root {
    --header-height: 50px; /* Reduced to match sticky header on mobile */
    --header-height-scrolled: 50px;
  }

  main > div:first-child,
  main > section:first-child {
    padding-top: 5px; /* Minimal padding */
  }
}
