/**
 * Test Data Generation Script
 * 
 * This script generates test data for MongoDB.
 * It creates users, businesses, reviews, and bookmarks.
 * 
 * Usage:
 * node scripts/generate-test-data.js
 */

require('dotenv').config();
const { MongoClient, ObjectId } = require('mongodb');
const bcrypt = require('bcryptjs');
const { faker } = require('@faker-js/faker');

// MongoDB configuration
const mongoUri = process.env.MONGODB_URI;
const dbName = process.env.MONGODB_DB_NAME || 'findapro';

// Connect to MongoDB
async function connectToMongoDB() {
  const client = new MongoClient(mongoUri);
  await client.connect();
  console.log('Connected to MongoDB');
  return { client, db: client.db(dbName) };
}

// Generate random users
function generateUsers(count) {
  const users = [];
  const roles = ['customer', 'business_owner', 'admin', 'super_admin', 'content_manager'];
  
  for (let i = 0; i < count; i++) {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    const email = faker.internet.email({ firstName, lastName }).toLowerCase();
    const password = bcrypt.hashSync('password123', 10);
    const role = i < 5 ? roles[i] : faker.helpers.arrayElement(['customer', 'business_owner']);
    
    users.push({
      _id: new ObjectId(),
      email,
      password,
      name: `${firstName} ${lastName}`,
      role,
      profileImage: faker.image.avatar(),
      phone: faker.phone.number(),
      address: faker.location.streetAddress(),
      city: faker.helpers.arrayElement(['Lagos', 'Abuja', 'Port Harcourt', 'Kano', 'Ibadan']),
      state: faker.helpers.arrayElement(['Lagos', 'FCT', 'Rivers', 'Kano', 'Oyo']),
      country: 'Nigeria',
      isEmailVerified: faker.datatype.boolean(0.8),
      createdAt: faker.date.past(),
      updatedAt: new Date()
    });
  }
  
  return users;
}

// Generate random businesses
function generateBusinesses(users, count) {
  const businesses = [];
  const categories = ['Restaurant', 'Hotel', 'Plumbing', 'Electrical', 'Carpentry', 'Cleaning', 'Landscaping', 'Painting', 'Roofing', 'HVAC'];
  const listingTypes = ['place', 'service', 'professional'];
  
  // Filter business owners
  const businessOwners = users.filter(user => 
    user.role === 'business_owner' || 
    user.role === 'admin' || 
    user.role === 'super_admin'
  );
  
  if (businessOwners.length === 0) {
    console.log('No business owners found. Cannot generate businesses.');
    return [];
  }
  
  for (let i = 0; i < count; i++) {
    const owner = faker.helpers.arrayElement(businessOwners);
    const category = faker.helpers.arrayElement(categories);
    const listingType = faker.helpers.arrayElement(listingTypes);
    const name = `${faker.company.name()} ${category}`;
    
    businesses.push({
      _id: new ObjectId(),
      name,
      description: faker.company.catchPhrase(),
      category,
      listingType,
      address: faker.location.streetAddress(),
      city: faker.helpers.arrayElement(['Lagos', 'Abuja', 'Port Harcourt', 'Kano', 'Ibadan']),
      state: faker.helpers.arrayElement(['Lagos', 'FCT', 'Rivers', 'Kano', 'Oyo']),
      zipCode: faker.location.zipCode(),
      country: 'Nigeria',
      phone: faker.phone.number(),
      email: faker.internet.email().toLowerCase(),
      website: faker.internet.url(),
      images: Array.from({ length: faker.number.int({ min: 1, max: 5 }) }, () => ({
        url: faker.image.urlLoremFlickr({ category: 'business' })
      })),
      ownerId: owner._id,
      rating: faker.number.float({ min: 0, max: 5, precision: 0.1 }),
      numReviews: faker.number.int({ min: 0, max: 50 }),
      isPremium: faker.datatype.boolean(0.3),
      isActive: faker.datatype.boolean(0.9),
      isApproved: faker.datatype.boolean(0.8),
      kybVerified: faker.helpers.arrayElement(['verified', 'pending', 'unverified']),
      createdAt: faker.date.past(),
      updatedAt: new Date()
    });
  }
  
  return businesses;
}

// Generate random reviews
function generateReviews(users, businesses, count) {
  const reviews = [];
  
  // Filter customers
  const customers = users.filter(user => user.role === 'customer');
  
  if (customers.length === 0 || businesses.length === 0) {
    console.log('No customers or businesses found. Cannot generate reviews.');
    return [];
  }
  
  // Create a set to track unique user-business pairs
  const reviewPairs = new Set();
  
  for (let i = 0; i < count; i++) {
    const user = faker.helpers.arrayElement(customers);
    const business = faker.helpers.arrayElement(businesses);
    const pairKey = `${user._id}-${business._id}`;
    
    // Skip if this user has already reviewed this business
    if (reviewPairs.has(pairKey)) {
      continue;
    }
    
    reviewPairs.add(pairKey);
    
    reviews.push({
      _id: new ObjectId(),
      userId: user._id,
      businessId: business._id,
      rating: faker.number.int({ min: 1, max: 5 }),
      comment: faker.lorem.paragraph(),
      createdAt: faker.date.past(),
      updatedAt: new Date()
    });
  }
  
  return reviews;
}

// Generate random bookmarks
function generateBookmarks(users, businesses, count) {
  const bookmarks = [];
  
  if (users.length === 0 || businesses.length === 0) {
    console.log('No users or businesses found. Cannot generate bookmarks.');
    return [];
  }
  
  // Create a set to track unique user-business pairs
  const bookmarkPairs = new Set();
  
  for (let i = 0; i < count; i++) {
    const user = faker.helpers.arrayElement(users);
    const business = faker.helpers.arrayElement(businesses);
    const pairKey = `${user._id}-${business._id}`;
    
    // Skip if this user has already bookmarked this business
    if (bookmarkPairs.has(pairKey)) {
      continue;
    }
    
    bookmarkPairs.add(pairKey);
    
    bookmarks.push({
      _id: new ObjectId(),
      userId: user._id,
      businessId: business._id,
      createdAt: faker.date.past(),
      updatedAt: new Date()
    });
  }
  
  return bookmarks;
}

// Update business ratings based on reviews
function updateBusinessRatings(businesses, reviews) {
  // Create a map to group reviews by business ID
  const reviewsByBusiness = {};
  
  reviews.forEach(review => {
    const businessId = review.businessId.toString();
    if (!reviewsByBusiness[businessId]) {
      reviewsByBusiness[businessId] = [];
    }
    reviewsByBusiness[businessId].push(review);
  });
  
  // Update business ratings
  businesses.forEach(business => {
    const businessId = business._id.toString();
    const businessReviews = reviewsByBusiness[businessId] || [];
    
    if (businessReviews.length > 0) {
      const totalRating = businessReviews.reduce((sum, review) => sum + review.rating, 0);
      const averageRating = totalRating / businessReviews.length;
      
      business.rating = parseFloat(averageRating.toFixed(1));
      business.numReviews = businessReviews.length;
    }
  });
  
  return businesses;
}

// Main function to generate and insert test data
async function generateTestData() {
  let client;
  
  try {
    // Connect to MongoDB
    const { client: mongoClient, db } = await connectToMongoDB();
    client = mongoClient;
    
    console.log('Generating test data...');
    
    // Generate users
    const userCount = 20;
    console.log(`Generating ${userCount} users...`);
    const users = generateUsers(userCount);
    
    // Insert users
    const userResult = await db.collection('users').insertMany(users);
    console.log(`${userResult.insertedCount} users inserted`);
    
    // Generate businesses
    const businessCount = 50;
    console.log(`Generating ${businessCount} businesses...`);
    const businesses = generateBusinesses(users, businessCount);
    
    // Insert businesses
    const businessResult = await db.collection('businesses').insertMany(businesses);
    console.log(`${businessResult.insertedCount} businesses inserted`);
    
    // Generate reviews
    const reviewCount = 100;
    console.log(`Generating ${reviewCount} reviews...`);
    const reviews = generateReviews(users, businesses, reviewCount);
    
    // Insert reviews
    const reviewResult = await db.collection('reviews').insertMany(reviews);
    console.log(`${reviewResult.insertedCount} reviews inserted`);
    
    // Generate bookmarks
    const bookmarkCount = 50;
    console.log(`Generating ${bookmarkCount} bookmarks...`);
    const bookmarks = generateBookmarks(users, businesses, bookmarkCount);
    
    // Insert bookmarks
    const bookmarkResult = await db.collection('bookmarks').insertMany(bookmarks);
    console.log(`${bookmarkResult.insertedCount} bookmarks inserted`);
    
    // Update business ratings based on reviews
    console.log('Updating business ratings based on reviews...');
    const updatedBusinesses = updateBusinessRatings(businesses, reviews);
    
    // Update businesses in the database
    for (const business of updatedBusinesses) {
      await db.collection('businesses').updateOne(
        { _id: business._id },
        { $set: { rating: business.rating, numReviews: business.numReviews } }
      );
    }
    
    console.log('Business ratings updated');
    
    console.log('\nTest data generation completed successfully');
    
    // Print sample credentials
    const adminUser = users.find(user => user.role === 'admin');
    const businessOwner = users.find(user => user.role === 'business_owner');
    const customer = users.find(user => user.role === 'customer');
    
    console.log('\nSample credentials:');
    console.log('Admin:', adminUser ? adminUser.email : 'N/A', '(password: password123)');
    console.log('Business Owner:', businessOwner ? businessOwner.email : 'N/A', '(password: password123)');
    console.log('Customer:', customer ? customer.email : 'N/A', '(password: password123)');
  } catch (error) {
    console.error('Error generating test data:', error);
  } finally {
    // Close MongoDB connection
    if (client) {
      await client.close();
      console.log('MongoDB connection closed');
    }
  }
}

// Run the test data generation
generateTestData();
