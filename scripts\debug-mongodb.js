/**
 * MongoDB Debug Script
 * 
 * This script helps debug MongoDB connection and query issues.
 * It tests the connection to MongoDB and performs basic operations.
 * 
 * Usage:
 * node scripts/debug-mongodb.js
 */

require('dotenv').config({ path: '.env.local' });
const mongoose = require('mongoose');
const { MongoClient } = require('mongodb');

// MongoDB configuration
const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/findapro';
const dbName = process.env.MONGODB_DB_NAME || 'findapro';

// Test MongoDB connection using MongoClient
async function testMongoClientConnection() {
  console.log('Testing MongoDB connection using MongoClient...');
  console.log(`MongoDB URI: ${mongoUri}`);
  
  const client = new MongoClient(mongoUri);
  
  try {
    await client.connect();
    console.log('✅ Successfully connected to MongoDB using MongoClient');
    
    const db = client.db(dbName);
    const collections = await db.listCollections().toArray();
    
    console.log(`\nFound ${collections.length} collections in the database:`);
    collections.forEach((collection, index) => {
      console.log(`${index + 1}. ${collection.name}`);
    });
    
    // Test basic operations
    if (collections.length > 0) {
      const collectionName = collections[0].name;
      console.log(`\nTesting basic operations on collection: ${collectionName}`);
      
      const collection = db.collection(collectionName);
      const count = await collection.countDocuments();
      console.log(`Collection has ${count} documents`);
      
      if (count > 0) {
        const sample = await collection.findOne();
        console.log('Sample document:');
        console.log(JSON.stringify(sample, null, 2));
      }
    }
    
    return true;
  } catch (error) {
    console.error('❌ Error connecting to MongoDB using MongoClient:', error);
    return false;
  } finally {
    await client.close();
    console.log('Closed MongoClient connection');
  }
}

// Test MongoDB connection using Mongoose
async function testMongooseConnection() {
  console.log('\nTesting MongoDB connection using Mongoose...');
  
  try {
    await mongoose.connect(mongoUri);
    console.log('✅ Successfully connected to MongoDB using Mongoose');
    
    // Check connection state
    const states = ['disconnected', 'connected', 'connecting', 'disconnecting'];
    console.log(`Connection state: ${states[mongoose.connection.readyState]}`);
    
    // Get all models
    const modelNames = mongoose.modelNames();
    console.log(`\nRegistered Mongoose models (${modelNames.length}):`);
    modelNames.forEach((modelName, index) => {
      console.log(`${index + 1}. ${modelName}`);
    });
    
    // Test basic operations on a model if available
    if (modelNames.length > 0) {
      const modelName = modelNames[0];
      console.log(`\nTesting basic operations on model: ${modelName}`);
      
      const Model = mongoose.model(modelName);
      const count = await Model.countDocuments();
      console.log(`Model has ${count} documents`);
      
      if (count > 0) {
        const sample = await Model.findOne();
        console.log('Sample document:');
        console.log(JSON.stringify(sample.toObject(), null, 2));
      }
    }
    
    return true;
  } catch (error) {
    console.error('❌ Error connecting to MongoDB using Mongoose:', error);
    return false;
  } finally {
    if (mongoose.connection.readyState) {
      await mongoose.connection.close();
      console.log('Closed Mongoose connection');
    }
  }
}

// Check MongoDB server info
async function checkMongoDBServerInfo() {
  console.log('\nChecking MongoDB server info...');
  
  const client = new MongoClient(mongoUri);
  
  try {
    await client.connect();
    
    const admin = client.db().admin();
    const serverInfo = await admin.serverInfo();
    
    console.log('MongoDB Server Info:');
    console.log(`- Version: ${serverInfo.version}`);
    console.log(`- Engine: ${serverInfo.modules?.length ? serverInfo.modules.join(', ') : 'Unknown'}`);
    
    // Get server status
    const serverStatus = await admin.serverStatus();
    console.log(`- Uptime: ${Math.floor(serverStatus.uptime / 86400)} days, ${Math.floor((serverStatus.uptime % 86400) / 3600)} hours`);
    console.log(`- Active connections: ${serverStatus.connections?.current || 'Unknown'}`);
    
    return true;
  } catch (error) {
    console.error('❌ Error getting MongoDB server info:', error);
    return false;
  } finally {
    await client.close();
  }
}

// Check environment variables
function checkEnvironmentVariables() {
  console.log('\nChecking environment variables...');
  
  const requiredVars = ['MONGODB_URI'];
  const missingVars = [];
  
  requiredVars.forEach(varName => {
    if (!process.env[varName]) {
      missingVars.push(varName);
    }
  });
  
  if (missingVars.length > 0) {
    console.error(`❌ Missing required environment variables: ${missingVars.join(', ')}`);
    console.log('Please check your .env file or environment configuration.');
    return false;
  }
  
  console.log('✅ All required environment variables are set');
  return true;
}

// Main function
async function debugMongoDB() {
  console.log('=== MongoDB Debug Tool ===\n');
  
  try {
    // Check environment variables
    const envCheck = checkEnvironmentVariables();
    
    // Test connections
    const clientConnected = await testMongoClientConnection();
    const mongooseConnected = await testMongooseConnection();
    
    // Check server info if connected
    let serverInfoCheck = false;
    if (clientConnected) {
      serverInfoCheck = await checkMongoDBServerInfo();
    }
    
    // Summary
    console.log('\n=== Debug Summary ===');
    console.log(`Environment Variables: ${envCheck ? '✅ OK' : '❌ Issues found'}`);
    console.log(`MongoClient Connection: ${clientConnected ? '✅ Connected' : '❌ Failed'}`);
    console.log(`Mongoose Connection: ${mongooseConnected ? '✅ Connected' : '❌ Failed'}`);
    console.log(`Server Info: ${serverInfoCheck ? '✅ Retrieved' : '❌ Not available'}`);
    
    if (!envCheck || !clientConnected || !mongooseConnected) {
      console.log('\n⚠️ Some issues were detected. Please check the logs above for details.');
    } else {
      console.log('\n✅ MongoDB connection is working correctly!');
    }
  } catch (error) {
    console.error('\n❌ Unexpected error during debugging:', error);
  }
}

// Run the debug function
debugMongoDB();
