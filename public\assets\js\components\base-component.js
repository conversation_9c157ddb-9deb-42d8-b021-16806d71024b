class BaseComponent {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        if (!this.container) {
            console.warn(`Container element with id "${containerId}" not found. Creating a default container.`);
            this.container = document.createElement('div');
            this.container.id = containerId;
            document.body.appendChild(this.container);
        }
        this.initialize(); // Call initialize to allow subclasses to do setup
    }

    initialize() {
        // To be implemented by child classes.  This is where you set up event
        // listeners and initial DOM manipulation.
    }

    async fetchData(url, options = {}) {
        try {
            const token = localStorage.getItem('token');
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers // Allow overriding headers
            };
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }

            const response = await fetch(url, { ...options, headers });

            if (!response.ok) {
                const errorData = await response.json(); // Attempt to get JSON error
                const errorMessage = errorData.message || `HTTP error! status: ${response.status}`;
                throw new Error(errorMessage); // Throw to be caught by caller
            }

            return await response.json(); // Parse and return JSON data

        } catch (error) {
            this.handleError(error);  // Consistent error handling
            throw error; // Re-throw so calling function can handle it too.
        }
    }


    handleError(error) {
        console.error('Error:', error);
        this.notify('error', error.message || 'An unexpected error occurred');
    }


    showLoader(message = 'Loading...') {
      const existingLoader = document.getElementById('global-loader');
      if(existingLoader) return; // prevent multiple

        const loader = document.createElement('div');
        loader.id = "global-loader";
        loader.className = 'loader-overlay'; // Add CSS classes for styling
        loader.innerHTML = `
            <div class="loader-content">
                <div class="spinner"></div>
                <p>${message}</p>
            </div>
        `;
        document.body.appendChild(loader);
    }

    hideLoader() {
        const loader = document.getElementById('global-loader');
        if (loader) {
            loader.remove();
        }
    }

    notify(type, message) {
        const container = document.getElementById('notification-container') || document.body; // Use a specific container or the body

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`; // e.g., 'notification success'
        notification.textContent = message;
        container.appendChild(notification);

        // Automatically remove the notification after a few seconds
        setTimeout(() => {
            notification.remove();
        }, 5000); // 5 seconds
    }

    addEventListeners(eventMap) {
      Object.entries(eventMap).forEach(([selector, events]) => {
        Object.entries(events).forEach(([eventType, handler]) => {
          this.container.addEventListener(eventType, (event) => {
            // Use closest() to handle event delegation.
            const target = event.target.closest(selector);
            if (target) {
              // Ensure 'this' refers to the component instance when calling the handler.
              handler.call(this, event, target);
            }
          });
        });
      });
    }

    debounce(func, wait) {
        let timeout;
        return (...args) => {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), wait);
        };
    }

    throttle(func, limit) {
        let inThrottle;
        return (...args) => {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }


    formatDate(dateString, options = {}) {
        if (!dateString) return '';
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString(undefined, options); // Use default locale, or specify
        }
        catch (error){
            console.error("Invalid date:", dateString);
            return "Invalid Date";
        }
    }

    formatCurrency(amount, currency = 'USD') {
      return new Intl.NumberFormat(undefined, {
          style: 'currency',
          currency: currency
        }).format(amount);
    }

    validateEmail(email) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    }
    validatePhone(phone){
       return /^\+?[\d\s-]{10,}$/.test(phone); // Basic phone validation
    }
     validateUrl(url) {
      try {
       new URL(url);
       return true;
      } catch {
       return false;
      }
   }
    slugify(text) {
      return text
        .toString()
        .toLowerCase()
        .trim()
        .replace(/\s+/g, '-')           // Replace spaces with -
        .replace(/[^\w\-]+/g, '')       // Remove all non-word chars
        .replace(/\-\-+/g, '-')         // Replace multiple - with single -
        .replace(/^-+/, '')             // Trim - from start of text
        .replace(/-+$/, '');            // Trim - from end of text
   }
   getCookie(name) {
      const value = `; ${document.cookie}`;
      const parts = value.split(`; ${name}=`);
      if (parts.length === 2) return parts.pop().split(';').shift();
   }

    setCookie(name, value, days = 7) {
       const date = new Date();
       date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
       const expires = `expires=${date.toUTCString()}`;
       document.cookie = `${name}=${value};${expires};path=/`; // Add samesite and secure attributes as needed.
   }

    deleteCookie(name) {
      document.cookie = `${name}=;Max-Age=-99999999;`; // Set expiration to the past
   }
    clearContainer() {
        while (this.container.firstChild) {
            this.container.removeChild(this.container.firstChild);
        }
    }
    removeEventListeners(events) {
		Object.entries(events).forEach(([selector, handlers]) => {
			const elements = this.container.querySelectorAll(selector); //select all elements that match
			elements.forEach(element => {
				Object.entries(handlers).forEach(([event, handler]) => {
					element.removeEventListener(event, handler.bind(this));
				});
			});
		});
    }
    destroy() {
        this.clearContainer();
    }

    static create(containerId) {
        return new this(containerId);
    }
}
export default BaseComponent;
