// components/search-bar.js
import { categories } from './categories.js';

/**
 * SearchBar component for handling search functionality across different listing types
 */
class SearchBar {
    /**
     * Initialize the search bar component
     * @param {string} containerId - ID of the container element where the search bar will be rendered
     */
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        if (!this.container) {
            console.error(`Container element with ID "${containerId}" not found.`);
            return;
        }
        this.currentType = 'place'; // Default listing type
        this.loadComponent();
    }

    /**
     * Load the search bar HTML component and initialize it
     */
    async loadComponent() {
        try {
            const response = await fetch('/assets/js/components/search-bar.html');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const html = await response.text();
            this.container.innerHTML = html;
            this.setupEventListeners();
            this.updateCategoryOptions('place'); // Initial category load
        } catch (error) {
            console.error('Error loading the search bar component:', error);
            this.container.innerHTML = '<p class="error-message">Error loading search component. Please refresh the page.</p>';
        }
    }

    /**
     * Set up event listeners for the search bar
     */
    setupEventListeners() {
        // Add event listeners to the tab buttons to update the category dropdown
        // and trigger a search when a tab is clicked
        this.container.querySelectorAll('.nav-link').forEach(tab => {
            tab.addEventListener('click', (event) => {
                const listingType = event.target.getAttribute('data-listing-type');
                if (listingType) {
                    this.currentType = listingType;
                    this.updateCategoryOptions(listingType);
                    this.handleSearchSubmit(event); // Call handleSearchSubmit to perform a new search
                }
            });
        });

        // Add event listener for the search form submission
        const searchForm = this.container.querySelector('[data-search-form]');
        if (searchForm) {
            searchForm.addEventListener('submit', this.handleSearchSubmit.bind(this));
        }

        // Add event listener for the "Use Current Location" button
        const locationButton = this.container.querySelector('.use-current-location');
        if (locationButton) {
            locationButton.addEventListener('click', () => {
                this.detectCurrentLocation();
            });
        }
    }

    /**
     * Handle search form submission
     * @param {Event} event - The form submission event
     */
    async handleSearchSubmit(event) {
        event.preventDefault();

        const form = this.container.querySelector('[data-search-form]');
        const formData = new FormData(form);
        const queryParams = new URLSearchParams();

        // Add parameters based on the form data and current tab
        queryParams.append('query', formData.get('query') || '');
        queryParams.append('location', formData.get('location') || '');
        queryParams.append('category', formData.get('category') || '');
        queryParams.append('listingType', this.currentType);

        // Redirect to the search page with parameters
        window.location.href = `search.html?${queryParams.toString()}`;
    }

    /**
     * Update category options in the dropdown based on the selected listing type
     * @param {string} listingType - The selected listing type
     */
    async updateCategoryOptions(listingType) {
        const categorySelect = this.container.querySelector('#categorySelect');
        if (!categorySelect) {
            console.warn('Category select element not found.'); // Better to warn than fail silently
            return;
        }
        categorySelect.innerHTML = '<option value="">Loading categories...</option>';

        try {
            const categoriesForType = categories[listingType];
            if (!categoriesForType) {
                throw new Error(`No categories defined for listing type: ${listingType}`);
            }
            // Clear existing options
            categorySelect.innerHTML = '<option value="">Choose Category</option>';

            // Add new options based on the selected listing type
            categoriesForType.forEach(category => {
                const option = document.createElement('option');
                option.value = category; // Use a unique identifier if you have one
                option.textContent = category; // Display the category name
                categorySelect.appendChild(option);
            });

        } catch (error) {
            console.error('Error loading categories:', error);
            categorySelect.innerHTML = '<option value="">Error loading categories</option>';
        }
    }

    /**
     * Detect the user's current location and populate the location input
     */
    async detectCurrentLocation() {
        const locationInput = this.container.querySelector('input[name="location"]');
        if (!locationInput) return;

        if (!navigator.geolocation) {
            alert('Geolocation is not supported by your browser');
            return;
        }

        locationInput.value = 'Detecting location...';
        locationInput.disabled = true;

        try {

            return new Promise((resolve, reject) => {
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        const { latitude, longitude } = position.coords;

                        // Option 1: Just use coordinates
                        locationInput.value = `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
                        locationInput.disabled = false;
                        resolve(`${latitude.toFixed(6)}, ${longitude.toFixed(6)}`);
                    },
                    (error) => {
                        console.error('Geolocation error:', error);
                        locationInput.value = '';
                        locationInput.disabled = false;
                        alert('Unable to get your location. Please enter it manually.');
                        reject(error);
                    }
                );
            });
        } catch (error) {
            console.error('Error getting location:', error);
            locationInput.value = '';
            locationInput.disabled = false;
            alert('Unable to get your location. Please enter it manually.');
        }
    }

    /**
     * Reverse geocode coordinates to get an address (placeholder for API integration)
     * @param {number} latitude - Latitude coordinate
     * @param {number} longitude - Longitude coordinate
     * @returns {Promise<string>} - The address string
     */
    async reverseGeocode(latitude, longitude) {
        // This would typically call a geocoding API like Google Maps, Mapbox, etc.
        // For now, we'll just return the coordinates
        return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
    }
}

// Initialize the search bar if the container exists when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const searchBarContainer = document.getElementById('search-bar-container');
    if (searchBarContainer) {
        new SearchBar('search-bar-container');
    }
});

export default SearchBar;
