#!/bin/bash

# Check if the source file path is provided
if [ -z "$1" ]; then
  echo "Error: Please provide the path to the JSON file"
  echo "Usage: ./copy_json.sh path/to/nigerian_states_with_cities_2025-04-01.json"
  exit 1
fi

# Copy the JSON file to the current directory
cp "$1" ./nigerian_states_with_cities.json

# Make the file readable
chmod 644 ./nigerian_states_with_cities.json

echo "JSON file copied to ./nigerian_states_with_cities.json"
