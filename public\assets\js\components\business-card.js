// business-card.js

/**
 * Generates HTML for star ratings based on a numeric rating
 * @param {number} rating - The rating value (0-5)
 * @returns {string} HTML string representing the stars
 */
function generateStars(rating) {
    let stars = '';
    for (let i = 1; i <= 5; i++) {
        if (i <= rating) {
            stars += '<i class="fas fa-star"></i>'; // Full star
        } else if (i - 0.5 === rating) {
            stars += '<i class="fas fa-star-half-alt"></i>'; // Half star
        } else {
            stars += '<i class="far fa-star"></i>'; // Empty star
        }
    }
    return stars;
}

/**
 * Fetches business data from the API and displays it in the specified container
 * @param {string} containerSelector - CSS selector for the container element
 * @param {Object} queryParams - Optional query parameters for filtering
 */
async function fetchAndDisplayBusinesses(containerSelector, queryParams = {}) {
    const container = document.querySelector(containerSelector);
    if (!container) {
        console.error('Container element not found:', containerSelector);
        return;
    }

    try {
        const url = new URL('/api/businesses', window.location.origin);
        Object.keys(queryParams).forEach(key => url.searchParams.append(key, queryParams[key]));

        const response = await fetch(url, {
            headers: {
                'Accept': 'application/json',
                // Add any other headers like authorization tokens here
            }
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message}`);
        }

        const data = await response.json(); // Assuming your API returns { businesses: [], totalCount: number }
        const businesses = data.businesses;

        if (!businesses || businesses.length === 0) {
            container.innerHTML = '<p>No listings found.</p>'; // Display a message if no listings
            return;
        }

        let cardsHTML = '';
        for (const business of businesses) {
            cardsHTML += createBusinessCard(business);
        }
        container.innerHTML = cardsHTML;

    } catch (error) {
        console.error('Could not fetch businesses:', error);
        container.innerHTML = `<p class="error-message">Error fetching listings: ${error.message}</p>`;
    }
}

/**
 * Creates HTML for a single business card
 * @param {Object} business - The business object containing all business data
 * @returns {string} HTML string for the business card
 */
function createBusinessCard(business) {
    // Generate rating HTML if the business has a rating
    let ratingHTML = '';
    if (business.averageRating) {
        ratingHTML = `
            <div class="Finda-rating overlay">
                <div class="Finda-pr-average high">${business.averageRating.toFixed(1)}</div>
                <div class="Finda-aldeio">
                    <div class="Finda-rates">
                        ${generateStars(business.averageRating)}
                    </div>
                    ${business.numReviews ? `<div class="Finda-all-review">(${business.numReviews} Reviews)</div>` : ''}
                </div>
            </div>
        `;
    }

    // Generate category HTML if the business has a category
    let categoryHTML = '';
    if (business.category) {
        categoryHTML = `<a href="#" class="Finda-cats-wrap"><div class="cats-ico bg-1"><i class="lni lni-list"></i></div><span class="cats-title">${business.category}</span></a>`;
    }

    // Return the complete business card HTML
    return `
        <div class="Finda-grid-wrap" data-listing-type="${business.listingType}">
            <div class="Finda-grid-upper">
               <div class="Finda-pos ab-left">
                 ${business.status ? `<div class="Finda-status close me-2">${business.status}</div>` : ''}
               </div>
                <div class="Finda-grid-thumb">
                    <a href="single-listing-detail-2.html?id=${business._id}" class="d-block text-center m-auto">
                    <img src="${business.images && business.images.length > 0 ? business.images[0].url : 'placeholder-image.jpg'}" class="img-fluid" alt="${business.name}">
                    </a>
                </div>
                ${ratingHTML}
            </div>
            <div class="Finda-grid-fl-wrap">
                <div class="Finda-caption px-3 py-2">
                    <div class="Finda-author">
                        <a href="#"><img src="${business.ownerId?.profilePicture || 'default-profile.jpg'}" class="img-fluid circle" alt=""></a>
                    </div>
                    <div class="Finda-cates multi">
                    </div>
                    <h4 class="mb-0 ft-medium medium"><a href="single-listing-detail-2.html?id=${business._id}" class="text-dark fs-md">${business.name}</a></h4>
                    <div class="Finda-middle-caption mt-3">
                        <div class="Finda-location"><i class="fas fa-map-marker-alt me-1 theme-cl"></i>${business.address}, ${business.city}, ${business.state}</div>
                        <div class="Finda-middle-caption mt-3">
                            ${business.description || ''}
                        </div>
                    </div>
                </div>
                <div class="Finda-grid-footer py-2 px-3">
                    <div class="Finda-ft-first">
                       ${categoryHTML}
                    </div>
                    <div class="Finda-ft-last">
                        <div class="Finda-inline">
                            <div class="Finda-bookmark-btn"><button type="button"><i class="lni lni-envelope position-absolute"></i></button></div>
                            <div class="Finda-bookmark-btn"><button type="button"><i class="lni lni-heart-filled position-absolute"></i></button></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Add event listener to initialize business cards when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Check if we're on a page that needs to display business listings
    const businessListingsContainer = document.getElementById('business-listings-container');
    if (businessListingsContainer) {
        // Get any query parameters from the URL
        const urlParams = new URLSearchParams(window.location.search);
        const queryParams = {};

        // Convert URL parameters to an object
        for (const [key, value] of urlParams.entries()) {
            queryParams[key] = value;
        }

        // Fetch and display businesses with any query parameters
        fetchAndDisplayBusinesses('#business-listings-container', queryParams);
    }
});

// Export functions for use in other modules
export { fetchAndDisplayBusinesses, createBusinessCard };
