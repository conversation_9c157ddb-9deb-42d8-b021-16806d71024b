'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import AdminLayout from '@/layouts/AdminLayout';
import { useAuth } from '@/hooks/useAuth';
import { FaArrowLeft, FaSave, FaPaperPlane, FaClock, FaUsers, FaRegCalendarAlt } from 'react-icons/fa';
import dynamic from 'next/dynamic';

// Import the rich text editor dynamically to avoid SSR issues
const TiptapEditor = dynamic(() => import('@/components/TiptapEditor'), { ssr: false });

export default function CreateNewsletter() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();

  const [subscribers, setSubscribers] = useState([]);
  const [subscriberSegments, setSubscriberSegments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // Form state
  const [formData, setFormData] = useState({
    subject: '',
    preheader: '',
    content: '',
    segmentId: 'all',
    scheduledFor: '',
    sendNow: true,
    testEmails: ''
  });

  // Form validation
  const [formErrors, setFormErrors] = useState({});

  useEffect(() => {
    if (authLoading) return;

    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch segments from API
        const segmentsResponse = await fetch('/api/admin/newsletter/segments');

        if (!segmentsResponse.ok) {
          throw new Error('Failed to fetch segments');
        }

        const segmentsData = await segmentsResponse.json();

        // Fetch active subscribers count
        const subscribersResponse = await fetch('/api/admin/newsletter/subscribers?status=active');

        if (!subscribersResponse.ok) {
          throw new Error('Failed to fetch subscribers');
        }

        const subscribersData = await subscribersResponse.json();

        // If API calls are successful but we're still developing, use mock data
        // Remove this in production
        const mockSegments = [
          { id: 'all', name: 'All Subscribers', count: 15 },
          { id: 'active', name: 'Active Subscribers', count: 10 },
          { id: 'new', name: 'New Subscribers (Last 30 Days)', count: 5 },
          { id: 'inactive', name: 'Inactive Subscribers (No Opens)', count: 3 }
        ];

        // Use API data if available, otherwise use mock data
        const segments = segmentsData.segments || [];

        // Add 'All Subscribers' option if not present
        if (!segments.find(seg => seg.id === 'all')) {
          segments.unshift({
            id: 'all',
            name: 'All Subscribers',
            count: subscribersData.pagination?.total || 15
          });
        }

        setSubscriberSegments(segments.length > 0 ? segments : mockSegments);
        setSubscribers(subscribersData.subscribers || []);

        // Set default scheduled date to tomorrow at 9 AM
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(9, 0, 0, 0);

        setFormData(prevData => ({
          ...prevData,
          scheduledFor: tomorrow.toISOString().slice(0, 16) // Format: YYYY-MM-DDThh:mm
        }));
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load required data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [authLoading]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prevData => ({
      ...prevData,
      [name]: value
    }));

    // Clear error for this field
    if (formErrors[name]) {
      setFormErrors(prevErrors => ({
        ...prevErrors,
        [name]: null
      }));
    }
  };

  const handleEditorChange = (content) => {
    setFormData(prevData => ({
      ...prevData,
      content
    }));

    // Clear error for content field
    if (formErrors.content) {
      setFormErrors(prevErrors => ({
        ...prevErrors,
        content: null
      }));
    }
  };

  const handleSendNowChange = (e) => {
    const sendNow = e.target.checked;
    setFormData(prevData => ({
      ...prevData,
      sendNow
    }));
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.subject.trim()) {
      errors.subject = 'Please enter a subject line';
    }

    if (!formData.content.trim()) {
      errors.content = 'Please enter newsletter content';
    }

    if (!formData.sendNow && !formData.scheduledFor) {
      errors.scheduledFor = 'Please select a date and time to schedule the newsletter';
    }

    if (formData.testEmails) {
      const emails = formData.testEmails.split(',').map(email => email.trim());
      const invalidEmails = emails.filter(email => !isValidEmail(email));

      if (invalidEmails.length > 0) {
        errors.testEmails = `Invalid email format: ${invalidEmails.join(', ')}`;
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const isValidEmail = (email) => {
    const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      // Scroll to the first error
      const firstErrorField = Object.keys(formErrors)[0];
      const element = document.querySelector(`[name="${firstErrorField}"]`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      return;
    }

    setSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Call API to create campaign
      const response = await fetch('/api/admin/newsletter/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          subject: formData.subject,
          preheader: formData.preheader,
          content: formData.content,
          segmentId: formData.segmentId,
          sendNow: formData.sendNow,
          scheduledFor: !formData.sendNow ? formData.scheduledFor : undefined
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create campaign');
      }

      // Show success message
      setSuccess(formData.sendNow
        ? 'Newsletter campaign sent successfully!'
        : 'Newsletter campaign scheduled successfully!');

      // Reset form after a delay
      setTimeout(() => {
        router.push('/admin/newsletter-analytics');
      }, 2000);
    } catch (err) {
      console.error('Error creating newsletter campaign:', err);
      setError(err.message || 'Failed to create newsletter campaign. Please try again later.');
      window.scrollTo(0, 0);
    } finally {
      setSubmitting(false);
    }
  };

  const handleSendTest = async () => {
    if (!formData.testEmails) {
      setFormErrors({
        ...formErrors,
        testEmails: 'Please enter at least one email address'
      });
      return;
    }

    const emails = formData.testEmails.split(',').map(email => email.trim());
    const invalidEmails = emails.filter(email => !isValidEmail(email));

    if (invalidEmails.length > 0) {
      setFormErrors({
        ...formErrors,
        testEmails: `Invalid email format: ${invalidEmails.join(', ')}`
      });
      return;
    }

    // Create a temporary campaign ID for testing
    // In a real implementation, we would save the draft first and then send a test
    const tempId = 'temp-' + Date.now();

    setSubmitting(true);
    setError(null);

    try {
      // Call API to send test email
      const response = await fetch(`/api/admin/newsletter/campaigns/${tempId}/send-test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          testEmails: emails,
          subject: formData.subject,
          preheader: formData.preheader,
          content: formData.content
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to send test email');
      }

      // Show success message
      alert(`Test newsletter sent to: ${emails.join(', ')}`);
    } catch (err) {
      console.error('Error sending test newsletter:', err);
      setError(err.message || 'Failed to send test newsletter. Please try again later.');
    } finally {
      setSubmitting(false);
    }
  };

  const getSegmentSubscriberCount = (segmentId) => {
    const segment = subscriberSegments.find(seg => seg.id === segmentId);
    return segment ? segment.count : 0;
  };

  if (authLoading || loading) {
    return (
      <AdminLayout>
        <div className="text-center py-5">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Loading...</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="container-fluid">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h1 className="h3 mb-0 text-gray-800">Create Newsletter Campaign</h1>
          <Link href="/admin/manage-newsletter" className="btn btn-outline-primary">
            <FaArrowLeft className="me-2" /> Back to Subscribers
          </Link>
        </div>

        {error && (
          <div className="alert alert-danger" role="alert">
            {error}
          </div>
        )}

        {success && (
          <div className="alert alert-success" role="alert">
            {success}
          </div>
        )}

        <div className="row">
          <div className="col-lg-8">
            <div className="card shadow mb-4">
              <div className="card-header py-3">
                <h6 className="m-0 font-weight-bold text-primary">Newsletter Content</h6>
              </div>
              <div className="card-body">
                <form onSubmit={handleSubmit}>
                  <div className="mb-3">
                    <label htmlFor="subject" className="form-label">Subject Line*</label>
                    <input
                      type="text"
                      className={`form-control ${formErrors.subject ? 'is-invalid' : ''}`}
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      placeholder="Enter newsletter subject line"
                    />
                    {formErrors.subject && (
                      <div className="invalid-feedback">
                        {formErrors.subject}
                      </div>
                    )}
                  </div>

                  <div className="mb-3">
                    <label htmlFor="preheader" className="form-label">Preheader (Optional)</label>
                    <input
                      type="text"
                      className="form-control"
                      id="preheader"
                      name="preheader"
                      value={formData.preheader}
                      onChange={handleInputChange}
                      placeholder="Brief summary shown in email clients"
                    />
                    <small className="text-muted">
                      This text appears in email clients after the subject line. Keep it under 100 characters.
                    </small>
                  </div>

                  <div className="mb-3">
                    <label htmlFor="content" className="form-label">Newsletter Content*</label>
                    <div className={formErrors.content ? 'is-invalid' : ''}>
                      <TiptapEditor
                        value={formData.content}
                        onChange={handleEditorChange}
                        placeholder="Write your newsletter content here..."
                        height="300px"
                        className="mb-3"
                      />
                    </div>
                    {formErrors.content && (
                      <div className="invalid-feedback d-block">
                        {formErrors.content}
                      </div>
                    )}
                  </div>

                  <div className="d-grid gap-2 d-md-flex justify-content-md-end mt-5">
                    <Link href="/admin/manage-newsletter" className="btn btn-secondary me-md-2">
                      Cancel
                    </Link>
                    <button
                      type="submit"
                      className="btn btn-primary"
                      disabled={submitting}
                    >
                      {submitting ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                          {formData.sendNow ? 'Sending...' : 'Scheduling...'}
                        </>
                      ) : (
                        <>
                          {formData.sendNow ? (
                            <>
                              <FaPaperPlane className="me-2" /> Send Newsletter
                            </>
                          ) : (
                            <>
                              <FaClock className="me-2" /> Schedule Newsletter
                            </>
                          )}
                        </>
                      )}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <div className="col-lg-4">
            <div className="card shadow mb-4">
              <div className="card-header py-3">
                <h6 className="m-0 font-weight-bold text-primary">Delivery Options</h6>
              </div>
              <div className="card-body">
                <div className="mb-3">
                  <label htmlFor="segmentId" className="form-label">
                    <FaUsers className="me-2" /> Recipient Segment
                  </label>
                  <select
                    className="form-select"
                    id="segmentId"
                    name="segmentId"
                    value={formData.segmentId}
                    onChange={handleInputChange}
                  >
                    {subscriberSegments.map(segment => (
                      <option key={segment.id} value={segment.id}>
                        {segment.name} ({segment.count})
                      </option>
                    ))}
                  </select>
                  <small className="text-muted">
                    This newsletter will be sent to {getSegmentSubscriberCount(formData.segmentId)} subscribers.
                  </small>
                </div>

                <div className="mb-3">
                  <div className="form-check form-switch">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      id="sendNow"
                      checked={formData.sendNow}
                      onChange={handleSendNowChange}
                    />
                    <label className="form-check-label" htmlFor="sendNow">
                      Send immediately
                    </label>
                  </div>
                </div>

                {!formData.sendNow && (
                  <div className="mb-3">
                    <label htmlFor="scheduledFor" className="form-label">
                      <FaRegCalendarAlt className="me-2" /> Schedule For
                    </label>
                    <input
                      type="datetime-local"
                      className={`form-control ${formErrors.scheduledFor ? 'is-invalid' : ''}`}
                      id="scheduledFor"
                      name="scheduledFor"
                      value={formData.scheduledFor}
                      onChange={handleInputChange}
                    />
                    {formErrors.scheduledFor && (
                      <div className="invalid-feedback">
                        {formErrors.scheduledFor}
                      </div>
                    )}
                    <small className="text-muted">
                      Select the date and time when the newsletter should be sent.
                    </small>
                  </div>
                )}

                <hr className="my-4" />

                <div className="mb-3">
                  <label htmlFor="testEmails" className="form-label">Send Test Email</label>
                  <div className="input-group">
                    <input
                      type="text"
                      className={`form-control ${formErrors.testEmails ? 'is-invalid' : ''}`}
                      id="testEmails"
                      name="testEmails"
                      value={formData.testEmails}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                    />
                    <button
                      type="button"
                      className="btn btn-outline-primary"
                      onClick={handleSendTest}
                      disabled={submitting}
                    >
                      Send Test
                    </button>
                  </div>
                  {formErrors.testEmails && (
                    <div className="invalid-feedback d-block">
                      {formErrors.testEmails}
                    </div>
                  )}
                  <small className="text-muted">
                    Separate multiple email addresses with commas.
                  </small>
                </div>
              </div>
            </div>

            <div className="card shadow mb-4">
              <div className="card-header py-3">
                <h6 className="m-0 font-weight-bold text-primary">Newsletter Tips</h6>
              </div>
              <div className="card-body">
                <ul className="list-group list-group-flush">
                  <li className="list-group-item">
                    <strong>Subject Line:</strong>
                    <ul className="small mt-1">
                      <li>Keep it under 50 characters</li>
                      <li>Avoid spam trigger words</li>
                      <li>Create a sense of urgency or curiosity</li>
                    </ul>
                  </li>
                  <li className="list-group-item">
                    <strong>Content:</strong>
                    <ul className="small mt-1">
                      <li>Use a clear hierarchy with headings</li>
                      <li>Include compelling images</li>
                      <li>Keep paragraphs short and scannable</li>
                      <li>Include clear call-to-action buttons</li>
                    </ul>
                  </li>
                  <li className="list-group-item">
                    <strong>Timing:</strong>
                    <ul className="small mt-1">
                      <li>Tuesday, Wednesday, and Thursday typically have the best open rates</li>
                      <li>10 AM and 2 PM are often optimal sending times</li>
                      <li>Test different times to find what works best for your audience</li>
                    </ul>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
