# Finda Scripts

This directory contains utility scripts for the Finda application.

## Prerequisites

1. Make sure you have set up your MongoDB database and have the following environment variables:
   - `MONGODB_URI`: Your MongoDB connection string
   - `JWT_SECRET`: Your JWT secret key

## Available Scripts

### Database Seeding

1. Install dependencies:

   ```bash
   npm install
   ```

2. Run the seeding script:

   ```bash
   node scripts/seed-database.js
   ```

## Notes

- The seeding script creates sample data for testing and development purposes.
- You can modify the script to create custom data as needed.
