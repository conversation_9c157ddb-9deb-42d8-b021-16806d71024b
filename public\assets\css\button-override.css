/* Button Override Styles
 * This file ensures that the Add Listing button has the correct green styling
 * It is loaded after <PERSON><PERSON><PERSON> to override its styles
 */

/* Override Bootstrap's btn-primary class */
.btn-primary,
.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active,
.btn-primary.active,
.btn-primary:active:focus,
.btn-primary:active:hover,
.btn-primary.active:focus,
.btn-primary.active:hover,
.show > .btn-primary.dropdown-toggle,
.show > .btn-primary.dropdown-toggle:focus,
.show > .btn-primary.dropdown-toggle:hover {
  background-color: #359e04 !important;
  border-color: #359e04 !important;
  color: #fff !important;
}

/* Specific override for Add Listing button */
.add-listing a,
.add-listing > a,
li.add-listing > a,
.nav-menu-social > li.add-listing > a,
a.btn-icon,
.btn-icon,
a:has(span:contains('Add Listing')),
a:has(span:contains('Add Listing')) {
  background-color: #359e04 !important;
  color: #fff !important;
  border-color: #359e04 !important;
  border-radius: 50px !important;
  padding: 8px 20px !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  border: none !important;
  box-shadow: 0 2px 5px rgba(53, 158, 4, 0.3) !important;
  text-decoration: none !important;
  font-weight: 500 !important;
}

/* Hover state */
.add-listing a:hover,
.add-listing > a:hover,
li.add-listing > a:hover,
.nav-menu-social > li.add-listing > a:hover,
a.btn-icon:hover,
.btn-icon:hover {
  background-color: #2a7c03 !important;
  color: #fff !important;
  border-color: #2a7c03 !important;
}

/* Green Add Listing Button class */
.green-add-listing-button {
  background-color: #359e04 !important;
  color: #fff !important;
  border-color: #359e04 !important;
  border-radius: 50px !important;
  padding: 8px 20px !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  border: none !important;
  box-shadow: 0 2px 5px rgba(53, 158, 4, 0.3) !important;
  text-decoration: none !important;
  font-weight: 500 !important;
}
