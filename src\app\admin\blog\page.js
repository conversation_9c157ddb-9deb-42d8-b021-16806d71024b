'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import AdminLayout from '@/layouts/AdminLayout';
import { useAuth } from '@/hooks/useAuth';

export default function AdminBlogPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [categories, setCategories] = useState([]);
  const [selectedPost, setSelectedPost] = useState(null);
  
  // Redirect if user is not logged in or not an admin
  useEffect(() => {
    if (!authLoading && (!user || (user.role !== 'admin' && user.role !== 'super_admin'))) {
      router.push('/login?redirect=/admin/blog');
    }
  }, [user, authLoading, router]);
  
  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/blog/categories');
        if (response.ok) {
          const data = await response.json();
          setCategories(data.categories);
        }
      } catch (err) {
        console.error('Error fetching categories:', err);
      }
    };
    
    fetchCategories();
  }, []);
  
  // Fetch blog posts
  useEffect(() => {
    const fetchPosts = async () => {
      if (!user) return;
      
      setLoading(true);
      setError(null);
      
      try {
        // Build query parameters
        const queryParams = new URLSearchParams();
        queryParams.append('page', currentPage);
        queryParams.append('limit', 10);
        
        if (searchTerm) {
          queryParams.append('search', searchTerm);
        }
        
        if (statusFilter !== 'all') {
          queryParams.append('status', statusFilter);
        }
        
        if (categoryFilter !== 'all') {
          queryParams.append('category', categoryFilter);
        }
        
        // Fetch posts
        const response = await fetch(`/api/blog?${queryParams.toString()}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch blog posts');
        }
        
        const data = await response.json();
        setPosts(data.posts);
        setTotalPages(data.pagination.pages);
        
      } catch (err) {
        console.error('Error fetching blog posts:', err);
        setError('Failed to load blog posts. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    if (user && (user.role === 'admin' || user.role === 'super_admin')) {
      fetchPosts();
    }
  }, [user, searchTerm, statusFilter, categoryFilter, currentPage]);
  
  // Handle post deletion
  const handleDeletePost = async (postId) => {
    if (!window.confirm('Are you sure you want to delete this post? This action cannot be undone.')) {
      return;
    }
    
    try {
      const response = await fetch(`/api/blog/${postId}`, {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete post');
      }
      
      // Remove post from state
      setPosts(posts.filter(post => post._id !== postId));
      
      // Close modal if the deleted post was selected
      if (selectedPost && selectedPost._id === postId) {
        setSelectedPost(null);
      }
      
      alert('Post deleted successfully');
    } catch (err) {
      console.error('Error deleting post:', err);
      alert('Failed to delete post. Please try again.');
    }
  };
  
  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };
  
  // Truncate text
  const truncateText = (text, maxLength = 100) => {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };
  
  if (authLoading || loading) {
    return (
      <AdminLayout>
        <div className="text-center py-5">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Loading blog posts...</p>
        </div>
      </AdminLayout>
    );
  }
  
  if (error) {
    return (
      <AdminLayout>
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      </AdminLayout>
    );
  }
  
  // If user is not logged in or not an admin, show nothing (will redirect)
  if (!user || (user.role !== 'admin' && user.role !== 'super_admin')) {
    return null;
  }
  
  return (
    <AdminLayout>
      <div className="admin-blog">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h1 className="h3 mb-0 text-gray-800">Blog Management</h1>
          <Link href="/admin/blog/new" className="btn btn-primary">
            <i className="fas fa-plus me-2"></i>Add New Post
          </Link>
        </div>
        
        {/* Filters */}
        <div className="card shadow mb-4">
          <div className="card-header py-3">
            <h6 className="m-0 font-weight-bold text-primary">Filters</h6>
          </div>
          <div className="card-body">
            <div className="row">
              <div className="col-md-4 mb-3">
                <div className="input-group">
                  <input 
                    type="text" 
                    className="form-control" 
                    placeholder="Search posts" 
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && setCurrentPage(1)}
                  />
                  <button 
                    className="btn btn-outline-secondary" 
                    type="button"
                    onClick={() => {
                      setSearchTerm('');
                      setCurrentPage(1);
                    }}
                  >
                    <i className="fas fa-times"></i>
                  </button>
                </div>
              </div>
              <div className="col-md-4 mb-3">
                <select 
                  className="form-select" 
                  value={statusFilter}
                  onChange={(e) => {
                    setStatusFilter(e.target.value);
                    setCurrentPage(1);
                  }}
                >
                  <option value="all">All Statuses</option>
                  <option value="published">Published</option>
                  <option value="draft">Draft</option>
                  <option value="archived">Archived</option>
                </select>
              </div>
              <div className="col-md-4 mb-3">
                <select 
                  className="form-select" 
                  value={categoryFilter}
                  onChange={(e) => {
                    setCategoryFilter(e.target.value);
                    setCurrentPage(1);
                  }}
                >
                  <option value="all">All Categories</option>
                  {categories.map(category => (
                    <option key={category._id} value={category.name}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>
        
        {/* Blog Posts Table */}
        <div className="card shadow mb-4">
          <div className="card-header py-3">
            <h6 className="m-0 font-weight-bold text-primary">Blog Posts</h6>
          </div>
          <div className="card-body">
            {posts.length === 0 ? (
              <div className="text-center py-5">
                <i className="fas fa-newspaper fa-3x text-gray-300 mb-3"></i>
                <p>No blog posts found. {searchTerm || statusFilter !== 'all' || categoryFilter !== 'all' ? 'Try adjusting your filters.' : ''}</p>
                <Link href="/admin/blog/new" className="btn btn-primary mt-2">
                  Create Your First Post
                </Link>
              </div>
            ) : (
              <div className="table-responsive">
                <table className="table table-bordered" width="100%" cellSpacing="0">
                  <thead>
                    <tr>
                      <th>Title</th>
                      <th>Category</th>
                      <th>Author</th>
                      <th>Status</th>
                      <th>Date</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {posts.map((post) => (
                      <tr key={post._id}>
                        <td>
                          <div className="d-flex align-items-center">
                            {post.featuredImage && (
                              <div className="post-thumbnail me-2">
                                <img 
                                  src={post.featuredImage} 
                                  alt={post.title} 
                                  width="50" 
                                  height="50" 
                                  style={{ objectFit: 'cover' }}
                                  className="rounded"
                                />
                              </div>
                            )}
                            <div>
                              <div className="post-title fw-bold">{post.title}</div>
                              <div className="post-excerpt text-muted small">
                                {truncateText(post.excerpt, 80)}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td>{post.category}</td>
                        <td>{post.authorId?.name || 'Unknown'}</td>
                        <td>
                          <span className={`badge ${
                            post.status === 'published' ? 'bg-success' : 
                            post.status === 'draft' ? 'bg-warning text-dark' : 
                            'bg-secondary'
                          }`}>
                            {post.status.charAt(0).toUpperCase() + post.status.slice(1)}
                          </span>
                        </td>
                        <td>{formatDate(post.createdAt)}</td>
                        <td>
                          <div className="btn-group">
                            <Link 
                              href={`/blog/${post.slug}`} 
                              className="btn btn-sm btn-info"
                              target="_blank"
                            >
                              <i className="fas fa-eye"></i>
                            </Link>
                            <Link 
                              href={`/admin/blog/edit/${post._id}`} 
                              className="btn btn-sm btn-primary"
                            >
                              <i className="fas fa-edit"></i>
                            </Link>
                            <button 
                              className="btn btn-sm btn-danger"
                              onClick={() => handleDeletePost(post._id)}
                            >
                              <i className="fas fa-trash"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
            
            {/* Pagination */}
            {totalPages > 1 && (
              <nav aria-label="Page navigation">
                <ul className="pagination justify-content-center mt-4">
                  <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                    <button 
                      className="page-link" 
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    >
                      Previous
                    </button>
                  </li>
                  
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                    <li key={page} className={`page-item ${currentPage === page ? 'active' : ''}`}>
                      <button 
                        className="page-link" 
                        onClick={() => setCurrentPage(page)}
                      >
                        {page}
                      </button>
                    </li>
                  ))}
                  
                  <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>
                    <button 
                      className="page-link" 
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    >
                      Next
                    </button>
                  </li>
                </ul>
              </nav>
            )}
          </div>
        </div>
        
        {/* Categories and Tags Management */}
        <div className="row">
          <div className="col-lg-6">
            <div className="card shadow mb-4">
              <div className="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 className="m-0 font-weight-bold text-primary">Categories</h6>
                <Link href="/admin/blog/categories" className="btn btn-sm btn-primary">
                  Manage Categories
                </Link>
              </div>
              <div className="card-body">
                <div className="categories-list">
                  {categories.length === 0 ? (
                    <p className="text-center text-muted">No categories found</p>
                  ) : (
                    <div className="row">
                      {categories.slice(0, 6).map(category => (
                        <div key={category._id} className="col-md-6 mb-2">
                          <div className="category-item">
                            <span className="category-name">{category.name}</span>
                            <span className="category-count badge bg-secondary ms-2">0</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          
          <div className="col-lg-6">
            <div className="card shadow mb-4">
              <div className="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 className="m-0 font-weight-bold text-primary">Tags</h6>
                <Link href="/admin/blog/tags" className="btn btn-sm btn-primary">
                  Manage Tags
                </Link>
              </div>
              <div className="card-body">
                <div className="tags-cloud">
                  {/* This would be populated with actual tags */}
                  <span className="badge bg-primary m-1 p-2">Technology</span>
                  <span className="badge bg-primary m-1 p-2">Business</span>
                  <span className="badge bg-primary m-1 p-2">Marketing</span>
                  <span className="badge bg-primary m-1 p-2">Design</span>
                  <span className="badge bg-primary m-1 p-2">Development</span>
                  <span className="badge bg-primary m-1 p-2">SEO</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
