#!/usr/bin/env node

/**
 * This script seeds the MongoDB database with sample data for testing and development.
 */

require('dotenv').config({ path: '.env.local' });
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const { faker } = require('@faker-js/faker');

// MongoDB connection
const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/finda';

// Models
let User, Business, Category, Review, Bookmark;

// Connect to MongoDB
async function connectToMongoDB() {
  try {
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');

    // Define schemas and models
    const userSchema = new mongoose.Schema({
      name: String,
      email: { type: String, unique: true },
      password: String,
      role: { type: String, enum: ['customer', 'business_owner', 'admin', 'super_admin', 'content_manager'] },
      isEmailVerified: <PERSON><PERSON><PERSON>,
      username: { type: String, unique: true },
      newsletterSubscribed: Boolean,
      isProfileCompleted: Boolean,
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date, default: Date.now }
    });

    const businessSchema = new mongoose.Schema({
      name: String,
      description: String,
      address: String,
      city: String,
      state: String,
      country: String,
      phone: String,
      email: String,
      website: String,
      categories: [String],
      tags: [String],
      openingHours: Object,
      isVerified: Boolean,
      isPremium: Boolean,
      isFeatured: Boolean,
      logo: String,
      images: [String],
      owner: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date, default: Date.now }
    });

    const categorySchema = new mongoose.Schema({
      name: String,
      slug: { type: String, unique: true },
      description: String,
      icon: String,
      parent: { type: mongoose.Schema.Types.ObjectId, ref: 'Category' },
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date, default: Date.now }
    });

    const reviewSchema = new mongoose.Schema({
      business: { type: mongoose.Schema.Types.ObjectId, ref: 'Business' },
      user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      rating: Number,
      comment: String,
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date, default: Date.now }
    });

    const bookmarkSchema = new mongoose.Schema({
      business: { type: mongoose.Schema.Types.ObjectId, ref: 'Business' },
      user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      createdAt: { type: Date, default: Date.now }
    });

    // Create models
    User = mongoose.model('User', userSchema);
    Business = mongoose.model('Business', businessSchema);
    Category = mongoose.model('Category', categorySchema);
    Review = mongoose.model('Review', reviewSchema);
    Bookmark = mongoose.model('Bookmark', bookmarkSchema);

  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
}

// Create sample users
async function createUsers() {
  try {
    console.log('Creating sample users...');

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('Password123!', salt);

    // Create admin user
    const adminUser = new User({
      name: 'Admin User',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin',
      isEmailVerified: true,
      username: 'admin',
      newsletterSubscribed: false,
      isProfileCompleted: true
    });

    // Create business owner
    const businessOwner = new User({
      name: 'Business Owner',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'business_owner',
      isEmailVerified: true,
      username: 'business_owner',
      newsletterSubscribed: true,
      isProfileCompleted: true
    });

    // Create customer
    const customer = new User({
      name: 'Test Customer',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'customer',
      isEmailVerified: true,
      username: 'customer',
      newsletterSubscribed: true,
      isProfileCompleted: true
    });

    // Save users
    await adminUser.save();
    await businessOwner.save();
    await customer.save();

    // Create additional random users
    const roles = ['customer', 'business_owner'];
    for (let i = 0; i < 10; i++) {
      const randomUser = new User({
        name: faker.person.fullName(),
        email: faker.internet.email(),
        password: hashedPassword,
        role: roles[Math.floor(Math.random() * roles.length)],
        isEmailVerified: Math.random() > 0.2, // 80% are verified
        username: faker.internet.username(),
        newsletterSubscribed: Math.random() > 0.5, // 50% subscribed
        isProfileCompleted: Math.random() > 0.3 // 70% completed profile
      });
      await randomUser.save();
    }

    console.log('Sample users created successfully');
    return { adminUser, businessOwner, customer };
  } catch (error) {
    console.error('Error creating users:', error);
    throw error;
  }
}

// Create sample categories
async function createCategories() {
  try {
    console.log('Creating sample categories...');

    const mainCategories = [
      { name: 'Restaurants', slug: 'restaurants', icon: 'restaurant' },
      { name: 'Hotels', slug: 'hotels', icon: 'hotel' },
      { name: 'Shopping', slug: 'shopping', icon: 'shopping_cart' },
      { name: 'Health & Beauty', slug: 'health-beauty', icon: 'spa' },
      { name: 'Services', slug: 'services', icon: 'build' }
    ];

    const savedCategories = [];
    for (const category of mainCategories) {
      const newCategory = new Category({
        name: category.name,
        slug: category.slug,
        description: `Find the best ${category.name.toLowerCase()} in your city`,
        icon: category.icon
      });
      await newCategory.save();
      savedCategories.push(newCategory);
    }

    // Create subcategories
    const subcategories = [
      { name: 'Fast Food', slug: 'fast-food', parent: 'restaurants', icon: 'fastfood' },
      { name: 'Fine Dining', slug: 'fine-dining', parent: 'restaurants', icon: 'restaurant_menu' },
      { name: 'Luxury Hotels', slug: 'luxury-hotels', parent: 'hotels', icon: 'stars' },
      { name: 'Budget Hotels', slug: 'budget-hotels', parent: 'hotels', icon: 'hotel' },
      { name: 'Clothing', slug: 'clothing', parent: 'shopping', icon: 'checkroom' },
      { name: 'Electronics', slug: 'electronics', parent: 'shopping', icon: 'devices' },
      { name: 'Spas', slug: 'spas', parent: 'health-beauty', icon: 'spa' },
      { name: 'Salons', slug: 'salons', parent: 'health-beauty', icon: 'content_cut' },
      { name: 'Plumbing', slug: 'plumbing', parent: 'services', icon: 'plumbing' },
      { name: 'Electrical', slug: 'electrical', parent: 'services', icon: 'electrical_services' }
    ];

    for (const subcategory of subcategories) {
      const parentCategory = savedCategories.find(c => c.slug === subcategory.parent);
      if (parentCategory) {
        const newSubcategory = new Category({
          name: subcategory.name,
          slug: subcategory.slug,
          description: `Find the best ${subcategory.name.toLowerCase()} in your city`,
          icon: subcategory.icon,
          parent: parentCategory._id
        });
        await newSubcategory.save();
      }
    }

    console.log('Sample categories created successfully');
    return savedCategories;
  } catch (error) {
    console.error('Error creating categories:', error);
    throw error;
  }
}

// Create sample businesses
async function createBusinesses(businessOwner, categories) {
  try {
    console.log('Creating sample businesses...');

    const cities = ['Lagos', 'Abuja', 'Port Harcourt', 'Kano', 'Ibadan', 'Enugu'];
    const states = ['Lagos', 'FCT', 'Rivers', 'Kano', 'Oyo', 'Enugu'];
    
    const savedBusinesses = [];
    
    // Create 20 sample businesses
    for (let i = 0; i < 20; i++) {
      const randomCityIndex = Math.floor(Math.random() * cities.length);
      const randomCategory = categories[Math.floor(Math.random() * categories.length)];
      
      const business = new Business({
        name: faker.company.name(),
        description: faker.company.catchPhrase(),
        address: faker.location.streetAddress(),
        city: cities[randomCityIndex],
        state: states[randomCityIndex],
        country: 'Nigeria',
        phone: faker.phone.number(),
        email: faker.internet.email(),
        website: faker.internet.url(),
        categories: [randomCategory.slug],
        tags: [faker.word.adjective(), faker.word.adjective(), faker.word.adjective()],
        openingHours: {
          monday: { open: '09:00', close: '17:00' },
          tuesday: { open: '09:00', close: '17:00' },
          wednesday: { open: '09:00', close: '17:00' },
          thursday: { open: '09:00', close: '17:00' },
          friday: { open: '09:00', close: '17:00' },
          saturday: { open: '10:00', close: '15:00' },
          sunday: { open: 'closed', close: 'closed' }
        },
        isVerified: Math.random() > 0.3, // 70% are verified
        isPremium: Math.random() > 0.7, // 30% are premium
        isFeatured: Math.random() > 0.8, // 20% are featured
        logo: `https://picsum.photos/seed/${i + 100}/200/200`,
        images: [
          `https://picsum.photos/seed/${i + 200}/800/600`,
          `https://picsum.photos/seed/${i + 300}/800/600`,
          `https://picsum.photos/seed/${i + 400}/800/600`
        ],
        owner: businessOwner._id
      });
      
      await business.save();
      savedBusinesses.push(business);
    }
    
    console.log('Sample businesses created successfully');
    return savedBusinesses;
  } catch (error) {
    console.error('Error creating businesses:', error);
    throw error;
  }
}

// Create sample reviews
async function createReviews(customer, businesses) {
  try {
    console.log('Creating sample reviews...');
    
    // Get all customers
    const customers = await User.find({ role: 'customer' });
    
    for (const business of businesses) {
      // Create 1-5 reviews per business
      const numReviews = Math.floor(Math.random() * 5) + 1;
      
      for (let i = 0; i < numReviews; i++) {
        // Select a random customer
        const randomCustomer = customers[Math.floor(Math.random() * customers.length)];
        
        const review = new Review({
          business: business._id,
          user: randomCustomer._id,
          rating: Math.floor(Math.random() * 5) + 1, // 1-5 stars
          comment: faker.lorem.paragraph(),
          createdAt: faker.date.past()
        });
        
        await review.save();
      }
    }
    
    console.log('Sample reviews created successfully');
  } catch (error) {
    console.error('Error creating reviews:', error);
    throw error;
  }
}

// Create sample bookmarks
async function createBookmarks(customer, businesses) {
  try {
    console.log('Creating sample bookmarks...');
    
    // Get all customers
    const customers = await User.find({ role: 'customer' });
    
    for (const customer of customers) {
      // Create 0-5 bookmarks per customer
      const numBookmarks = Math.floor(Math.random() * 6);
      
      // Shuffle businesses and take the first numBookmarks
      const shuffledBusinesses = [...businesses].sort(() => 0.5 - Math.random());
      const selectedBusinesses = shuffledBusinesses.slice(0, numBookmarks);
      
      for (const business of selectedBusinesses) {
        const bookmark = new Bookmark({
          business: business._id,
          user: customer._id,
          createdAt: faker.date.past()
        });
        
        await bookmark.save();
      }
    }
    
    console.log('Sample bookmarks created successfully');
  } catch (error) {
    console.error('Error creating bookmarks:', error);
    throw error;
  }
}

// Main function
async function main() {
  try {
    await connectToMongoDB();
    
    // Clear existing data
    console.log('Clearing existing data...');
    await User.deleteMany({});
    await Business.deleteMany({});
    await Category.deleteMany({});
    await Review.deleteMany({});
    await Bookmark.deleteMany({});
    
    // Create sample data
    const { adminUser, businessOwner, customer } = await createUsers();
    const categories = await createCategories();
    const businesses = await createBusinesses(businessOwner, categories);
    await createReviews(customer, businesses);
    await createBookmarks(customer, businesses);
    
    console.log('Database seeded successfully!');
    
    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    
    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
}

// Run the main function
main();
