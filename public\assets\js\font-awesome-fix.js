// This script ensures Font Awesome icons are properly initialized
document.addEventListener('DOMContentLoaded', function() {
  // Check if Font Awesome is loaded
  if (typeof FontAwesome === 'undefined' && typeof window.FontAwesome === 'undefined') {
    console.warn('Font Awesome not detected, attempting to load it');
    
    // Create a new link element for Font Awesome CSS
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css';
    document.head.appendChild(link);
    
    // Create a new script element for Font Awesome JS
    const script = document.createElement('script');
    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js';
    script.defer = true;
    document.head.appendChild(script);
  }
  
  // Force refresh icons
  setTimeout(function() {
    if (typeof window.FontAwesome !== 'undefined' && typeof window.FontAwesome.dom !== 'undefined') {
      window.FontAwesome.dom.i2svg();
    }
  }, 1000);
});
