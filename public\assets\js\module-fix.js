// Define exports and module.exports if they don't exist
if (typeof exports === 'undefined') {
  window.exports = {};
}
if (typeof module === 'undefined') {
  window.module = { exports: {} };
}

// Patch webpack chunk loading if available
if (window.__webpack_require__) {
  const originalLoadScript = window.__webpack_require__.l;
  
  window.__webpack_require__.l = function(url, done, key, chunkId) {
    // Add retry logic for script loading
    const maxRetries = 3;
    let retries = 0;
    
    function loadWithRetry() {
      originalLoadScript(url, function(event) {
        if (event && event.type === 'error') {
          if (retries < maxRetries) {
            console.warn(`Chunk loading failed for ${url}, retrying (${retries + 1}/${maxRetries})...`);
            retries++;
            setTimeout(loadWithRetry, 1000 * retries); // Exponential backoff
            return;
          }
        }
        done(event);
      }, key, chunkId);
    }
    
    loadWithRetry();
  };
}
