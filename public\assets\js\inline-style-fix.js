// <PERSON><PERSON><PERSON> to add a style tag to the head and apply inline styles
document.addEventListener('DOMContentLoaded', function() {
  // Create a style tag
  const styleTag = document.createElement('style');

  // Add the CSS content
  styleTag.innerHTML = `
    /* Target the Add Listing button specifically */
    .add-listing a,
    .add-listing > a,
    li.add-listing > a,
    .nav-menu-social > li.add-listing > a,
    a.btn-icon,
    a.btn-primary,
    .btn-icon,
    .btn-primary,
    .add-listing-button,
    .green-button {
      background-color: #359e04 !important;
      color: #fff !important;
      border-color: #359e04 !important;
      border-radius: 50px !important;
      padding: 8px 20px !important;
      display: flex !important;
      align-items: center !important;
      gap: 8px !important;
      border: none !important;
      box-shadow: 0 2px 5px rgba(53, 158, 4, 0.3) !important;
      text-decoration: none !important;
      font-weight: 500 !important;
    }
  `;

  // Append the style tag to the head
  document.head.appendChild(styleTag);

  // Function to apply inline styles to Add Listing buttons
  function applyInlineStyles() {
    // Find all Add Listing buttons by text content
    const allLinks = document.querySelectorAll('a');
    allLinks.forEach(link => {
      if (link.textContent.trim() === 'Add Listing' ||
          link.innerHTML.includes('Add Listing') ||
          (link.parentElement && link.parentElement.classList.contains('add-listing'))) {

        // Apply inline styles directly to the element
        link.setAttribute('style', `
          background-color: #359e04 !important;
          color: #fff !important;
          border-color: #359e04 !important;
          border-radius: 50px !important;
          padding: 8px 20px !important;
          display: flex !important;
          align-items: center !important;
          gap: 8px !important;
          border: none !important;
          box-shadow: 0 2px 5px rgba(53, 158, 4, 0.3) !important;
          text-decoration: none !important;
          font-weight: 500 !important;
        `);
      }
    });
  }

  // Run immediately
  applyInlineStyles();

  // Also run after a short delay to catch any dynamically loaded buttons
  setTimeout(applyInlineStyles, 500);
  setTimeout(applyInlineStyles, 1000);
  setTimeout(applyInlineStyles, 2000);
});
