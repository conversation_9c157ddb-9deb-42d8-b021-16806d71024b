// include-footer.js

/**
 * This script loads the footer.html content into any element with the id "footer-container"
 * It should be included in all pages that need the footer
 */
document.addEventListener('DOMContentLoaded', async () => {
    const footerContainer = document.getElementById('footer-container');
    
    if (footerContainer) {
        try {
            const response = await fetch('/footer.html');
            if (!response.ok) {
                throw new Error(`Failed to load footer: ${response.status}`);
            }
            
            const html = await response.text();
            footerContainer.innerHTML = html;
            
            // Load the newsletter.js script after the HTML is inserted
            const script = document.createElement('script');
            script.type = 'module';
            script.src = '/assets/js/newsletter.js';
            document.body.appendChild(script);
            
        } catch (error) {
            console.error('Error loading footer:', error);
            footerContainer.innerHTML = '<p>Error loading footer. Please refresh the page.</p>';
        }
    }
});
