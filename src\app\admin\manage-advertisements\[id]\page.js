'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import AdminLayout from '@/layouts/AdminLayout';
import { useAuth } from '@/hooks/useAuth';
import { FaArrowLeft, FaEdit, FaEye, FaCheck, FaTimes, FaTrash, FaExternalLinkAlt, FaCalendarAlt, FaMoneyBillWave, FaMapMarkerAlt, FaTag, FaChartLine } from 'react-icons/fa';

export default function ViewAdvertisement() {
  const { id } = useParams();
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  
  const [advertisement, setAdvertisement] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [newStatus, setNewStatus] = useState('');
  const [actionLoading, setActionLoading] = useState(false);
  
  useEffect(() => {
    if (authLoading) return;
    
    const fetchAdvertisement = async () => {
      setLoading(true);
      setError(null);
      
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Mock advertisement data
        const mockAd = {
          id: id,
          businessId: {
            id: 'biz1',
            name: 'Cafe Delight',
            category: 'Restaurant',
            email: '<EMAIL>',
            phone: '+234 ************'
          },
          imageUrl: '/assets/img/ads/ad1.jpg',
          clickUrl: 'https://example.com/cafe-delight',
          startDate: '2023-07-01',
          endDate: '2023-08-01',
          targetingCriteria: {
            categories: ['restaurant', 'food', 'dining'],
            locations: ['Lagos', 'Abuja']
          },
          budget: 50000,
          clicks: 245,
          impressions: 5678,
          status: 'active',
          createdAt: '2023-06-15T10:30:00Z',
          updatedAt: '2023-06-15T10:30:00Z',
          dailyStats: [
            { date: '2023-07-01', impressions: 187, clicks: 8 },
            { date: '2023-07-02', impressions: 203, clicks: 12 },
            { date: '2023-07-03', impressions: 178, clicks: 9 },
            { date: '2023-07-04', impressions: 195, clicks: 11 },
            { date: '2023-07-05', impressions: 210, clicks: 14 },
            { date: '2023-07-06', impressions: 225, clicks: 16 },
            { date: '2023-07-07', impressions: 198, clicks: 10 }
          ]
        };
        
        setAdvertisement(mockAd);
        setNewStatus(mockAd.status);
      } catch (err) {
        console.error('Error fetching advertisement:', err);
        setError('Failed to load advertisement details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchAdvertisement();
  }, [id, authLoading]);
  
  const handleStatusChange = async () => {
    setActionLoading(true);
    
    try {
      // In a real implementation, this would be an API call
      console.log(`Updating advertisement ${id} status to ${newStatus}`);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Update advertisement in state
      setAdvertisement({
        ...advertisement,
        status: newStatus,
        updatedAt: new Date().toISOString()
      });
      
      // Close modal
      setShowStatusModal(false);
    } catch (err) {
      console.error('Error updating advertisement status:', err);
      alert('Failed to update advertisement status. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };
  
  const handleDeleteAdvertisement = async () => {
    setActionLoading(true);
    
    try {
      // In a real implementation, this would be an API call
      console.log(`Deleting advertisement ${id}`);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Redirect to advertisements list
      router.push('/admin/manage-advertisements');
    } catch (err) {
      console.error('Error deleting advertisement:', err);
      alert('Failed to delete advertisement. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };
  
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };
  
  const formatDateTime = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' };
    return new Date(dateString).toLocaleString(undefined, options);
  };
  
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0
    }).format(amount);
  };
  
  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'active':
        return 'bg-success';
      case 'pending':
        return 'bg-warning text-dark';
      case 'expired':
        return 'bg-danger';
      case 'rejected':
        return 'bg-danger';
      default:
        return 'bg-secondary';
    }
  };
  
  // Calculate CTR (Click-Through Rate)
  const calculateCTR = (clicks, impressions) => {
    if (impressions === 0) return '0.00%';
    const ctr = (clicks / impressions) * 100;
    return ctr.toFixed(2) + '%';
  };
  
  if (authLoading || loading) {
    return (
      <AdminLayout>
        <div className="text-center py-5">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Loading advertisement details...</p>
        </div>
      </AdminLayout>
    );
  }
  
  if (error) {
    return (
      <AdminLayout>
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
        <div className="text-center mt-3">
          <Link href="/admin/manage-advertisements" className="btn btn-primary">
            <FaArrowLeft className="me-2" /> Back to Advertisements
          </Link>
        </div>
      </AdminLayout>
    );
  }
  
  if (!advertisement) {
    return (
      <AdminLayout>
        <div className="alert alert-warning" role="alert">
          Advertisement not found.
        </div>
        <div className="text-center mt-3">
          <Link href="/admin/manage-advertisements" className="btn btn-primary">
            <FaArrowLeft className="me-2" /> Back to Advertisements
          </Link>
        </div>
      </AdminLayout>
    );
  }
  
  return (
    <AdminLayout>
      <div className="container-fluid">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h1 className="h3 mb-0 text-gray-800">Advertisement Details</h1>
          <div>
            <Link href="/admin/manage-advertisements" className="btn btn-outline-primary me-2">
              <FaArrowLeft className="me-2" /> Back to Advertisements
            </Link>
            <Link href={`/admin/manage-advertisements/${id}/edit`} className="btn btn-primary">
              <FaEdit className="me-2" /> Edit Advertisement
            </Link>
          </div>
        </div>
        
        <div className="row">
          {/* Advertisement Information */}
          <div className="col-lg-8">
            <div className="card shadow mb-4">
              <div className="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 className="m-0 font-weight-bold text-primary">Advertisement Information</h6>
                <div>
                  {advertisement.status === 'pending' && (
                    <>
                      <button 
                        className="btn btn-sm btn-success me-2" 
                        onClick={() => {
                          setNewStatus('active');
                          setShowStatusModal(true);
                        }}
                      >
                        <FaCheck className="me-1" /> Approve
                      </button>
                      <button 
                        className="btn btn-sm btn-danger me-2" 
                        onClick={() => {
                          setNewStatus('rejected');
                          setShowStatusModal(true);
                        }}
                      >
                        <FaTimes className="me-1" /> Reject
                      </button>
                    </>
                  )}
                  {advertisement.status === 'active' && (
                    <button 
                      className="btn btn-sm btn-warning me-2" 
                      onClick={() => {
                        setNewStatus('expired');
                        setShowStatusModal(true);
                      }}
                    >
                      <FaTimes className="me-1" /> Deactivate
                    </button>
                  )}
                  <button 
                    className="btn btn-sm btn-danger" 
                    onClick={() => setShowDeleteModal(true)}
                  >
                    <FaTrash className="me-1" /> Delete
                  </button>
                </div>
              </div>
              <div className="card-body">
                <div className="row mb-4">
                  <div className="col-md-6">
                    <div className="ad-detail-item">
                      <div className="ad-detail-label">
                        <FaTag className="me-2" /> Business
                      </div>
                      <div className="ad-detail-value">
                        <Link href={`/admin/manage-businesses/${advertisement.businessId.id}`} className="text-decoration-none">
                          {advertisement.businessId.name}
                        </Link>
                        <div className="small text-muted">{advertisement.businessId.category}</div>
                        <div className="small text-muted">{advertisement.businessId.email}</div>
                        <div className="small text-muted">{advertisement.businessId.phone}</div>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="ad-detail-item">
                      <div className="ad-detail-label">
                        Status
                      </div>
                      <div className="ad-detail-value">
                        <span className={`badge ${getStatusBadgeClass(advertisement.status)}`}>
                          {advertisement.status.charAt(0).toUpperCase() + advertisement.status.slice(1)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="row mb-4">
                  <div className="col-md-12">
                    <div className="ad-detail-item">
                      <div className="ad-detail-label">
                        <FaEye className="me-2" /> Advertisement Preview
                      </div>
                      <div className="ad-detail-value">
                        <div className="ad-preview text-center p-3 bg-light rounded">
                          <div className="position-relative mb-3">
                            <Image 
                              src={advertisement.imageUrl || '/assets/img/placeholder-ad.jpg'} 
                              alt="Advertisement" 
                              width={400} 
                              height={210} 
                              className="img-fluid rounded"
                            />
                            <span className="position-absolute top-0 end-0 badge bg-secondary m-2">Ad</span>
                          </div>
                          <div className="small text-muted mb-2">
                            {advertisement.businessId.name}
                          </div>
                          <div className="small text-truncate">
                            <a href={advertisement.clickUrl} target="_blank" rel="noopener noreferrer" className="text-decoration-none">
                              {advertisement.clickUrl}
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="row mb-4">
                  <div className="col-md-6">
                    <div className="ad-detail-item">
                      <div className="ad-detail-label">
                        <FaLink className="me-2" /> Destination URL
                      </div>
                      <div className="ad-detail-value">
                        <div className="d-flex align-items-center">
                          <div className="text-truncate">
                            <a href={advertisement.clickUrl} target="_blank" rel="noopener noreferrer" className="text-decoration-none">
                              {advertisement.clickUrl}
                            </a>
                          </div>
                          <a 
                            href={advertisement.clickUrl} 
                            target="_blank" 
                            rel="noopener noreferrer" 
                            className="btn btn-sm btn-outline-primary ms-2"
                          >
                            <FaExternalLinkAlt />
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="ad-detail-item">
                      <div className="ad-detail-label">
                        <FaMoneyBillWave className="me-2" /> Budget
                      </div>
                      <div className="ad-detail-value">
                        {formatCurrency(advertisement.budget)}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="row mb-4">
                  <div className="col-md-6">
                    <div className="ad-detail-item">
                      <div className="ad-detail-label">
                        <FaCalendarAlt className="me-2" /> Duration
                      </div>
                      <div className="ad-detail-value">
                        <div>
                          <strong>Start Date:</strong> {formatDate(advertisement.startDate)}
                        </div>
                        <div>
                          <strong>End Date:</strong> {formatDate(advertisement.endDate)}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="ad-detail-item">
                      <div className="ad-detail-label">
                        <FaCalendarAlt className="me-2" /> Created/Updated
                      </div>
                      <div className="ad-detail-value">
                        <div>
                          <strong>Created:</strong> {formatDateTime(advertisement.createdAt)}
                        </div>
                        <div>
                          <strong>Last Updated:</strong> {formatDateTime(advertisement.updatedAt)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="row">
                  <div className="col-md-6">
                    <div className="ad-detail-item">
                      <div className="ad-detail-label">
                        <FaTag className="me-2" /> Target Categories
                      </div>
                      <div className="ad-detail-value">
                        {advertisement.targetingCriteria.categories.map((category, index) => (
                          <span key={index} className="badge bg-info me-1 mb-1">
                            {category}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="ad-detail-item">
                      <div className="ad-detail-label">
                        <FaMapMarkerAlt className="me-2" /> Target Locations
                      </div>
                      <div className="ad-detail-value">
                        {advertisement.targetingCriteria.locations.map((location, index) => (
                          <span key={index} className="badge bg-secondary me-1 mb-1">
                            {location}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Performance Metrics */}
          <div className="col-lg-4">
            <div className="card shadow mb-4">
              <div className="card-header py-3">
                <h6 className="m-0 font-weight-bold text-primary">Performance Metrics</h6>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-md-6 mb-4">
                    <div className="card border-left-primary h-100 py-2">
                      <div className="card-body">
                        <div className="row no-gutters align-items-center">
                          <div className="col mr-2">
                            <div className="text-xs font-weight-bold text-primary text-uppercase mb-1">
                              Impressions
                            </div>
                            <div className="h5 mb-0 font-weight-bold text-gray-800">
                              {advertisement.impressions.toLocaleString()}
                            </div>
                          </div>
                          <div className="col-auto">
                            <i className="fas fa-eye fa-2x text-gray-300"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-6 mb-4">
                    <div className="card border-left-success h-100 py-2">
                      <div className="card-body">
                        <div className="row no-gutters align-items-center">
                          <div className="col mr-2">
                            <div className="text-xs font-weight-bold text-success text-uppercase mb-1">
                              Clicks
                            </div>
                            <div className="h5 mb-0 font-weight-bold text-gray-800">
                              {advertisement.clicks.toLocaleString()}
                            </div>
                          </div>
                          <div className="col-auto">
                            <i className="fas fa-mouse-pointer fa-2x text-gray-300"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="row">
                  <div className="col-md-6 mb-4">
                    <div className="card border-left-info h-100 py-2">
                      <div className="card-body">
                        <div className="row no-gutters align-items-center">
                          <div className="col mr-2">
                            <div className="text-xs font-weight-bold text-info text-uppercase mb-1">
                              CTR
                            </div>
                            <div className="h5 mb-0 font-weight-bold text-gray-800">
                              {calculateCTR(advertisement.clicks, advertisement.impressions)}
                            </div>
                          </div>
                          <div className="col-auto">
                            <i className="fas fa-percentage fa-2x text-gray-300"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-6 mb-4">
                    <div className="card border-left-warning h-100 py-2">
                      <div className="card-body">
                        <div className="row no-gutters align-items-center">
                          <div className="col mr-2">
                            <div className="text-xs font-weight-bold text-warning text-uppercase mb-1">
                              Cost Per Click
                            </div>
                            <div className="h5 mb-0 font-weight-bold text-gray-800">
                              {advertisement.clicks > 0 
                                ? formatCurrency(advertisement.budget / advertisement.clicks) 
                                : formatCurrency(0)}
                            </div>
                          </div>
                          <div className="col-auto">
                            <i className="fas fa-money-bill fa-2x text-gray-300"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="performance-chart mb-4">
                  <h6 className="font-weight-bold">Daily Performance</h6>
                  <div className="table-responsive">
                    <table className="table table-sm">
                      <thead>
                        <tr>
                          <th>Date</th>
                          <th>Impressions</th>
                          <th>Clicks</th>
                          <th>CTR</th>
                        </tr>
                      </thead>
                      <tbody>
                        {advertisement.dailyStats.map((day, index) => (
                          <tr key={index}>
                            <td>{formatDate(day.date)}</td>
                            <td>{day.impressions}</td>
                            <td>{day.clicks}</td>
                            <td>{calculateCTR(day.clicks, day.impressions)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
                
                <div className="actions mt-4">
                  <h6 className="font-weight-bold">Actions</h6>
                  <div className="d-grid gap-2">
                    <Link href={`/admin/manage-advertisements/${id}/edit`} className="btn btn-primary">
                      <FaEdit className="me-2" /> Edit Advertisement
                    </Link>
                    <button 
                      className="btn btn-outline-danger"
                      onClick={() => setShowDeleteModal(true)}
                    >
                      <FaTrash className="me-2" /> Delete Advertisement
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Status Update Modal */}
      {showStatusModal && (
        <div className="modal fade show" style={{ display: 'block' }} tabIndex="-1">
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Update Advertisement Status</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setShowStatusModal(false)}
                ></button>
              </div>
              <div className="modal-body">
                <p>Are you sure you want to change the advertisement status to <strong>{newStatus}</strong>?</p>
                <div className="form-group">
                  <label htmlFor="statusSelect">Status:</label>
                  <select
                    id="statusSelect"
                    className="form-select mt-2"
                    value={newStatus}
                    onChange={(e) => setNewStatus(e.target.value)}
                  >
                    <option value="pending">Pending</option>
                    <option value="active">Active</option>
                    <option value="expired">Expired</option>
                    <option value="rejected">Rejected</option>
                  </select>
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => setShowStatusModal(false)}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn btn-primary"
                  onClick={handleStatusChange}
                  disabled={actionLoading}
                >
                  {actionLoading ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                      Updating...
                    </>
                  ) : (
                    'Update Status'
                  )}
                </button>
              </div>
            </div>
          </div>
          <div className="modal-backdrop fade show"></div>
        </div>
      )}
      
      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="modal fade show" style={{ display: 'block' }} tabIndex="-1">
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Delete Advertisement</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setShowDeleteModal(false)}
                ></button>
              </div>
              <div className="modal-body">
                <div className="alert alert-danger">
                  <i className="fas fa-exclamation-triangle me-2"></i>
                  Warning: This action cannot be undone.
                </div>
                <p>Are you sure you want to delete this advertisement?</p>
                <p><strong>Business:</strong> {advertisement.businessId.name}</p>
                <p><strong>Status:</strong> {advertisement.status}</p>
                <p><strong>Duration:</strong> {formatDate(advertisement.startDate)} to {formatDate(advertisement.endDate)}</p>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => setShowDeleteModal(false)}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn btn-danger"
                  onClick={handleDeleteAdvertisement}
                  disabled={actionLoading}
                >
                  {actionLoading ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                      Deleting...
                    </>
                  ) : (
                    'Delete Advertisement'
                  )}
                </button>
              </div>
            </div>
          </div>
          <div className="modal-backdrop fade show"></div>
        </div>
      )}
      
      <style jsx>{`
        .ad-detail-item {
          margin-bottom: 1rem;
        }
        
        .ad-detail-label {
          font-weight: 600;
          color: #4e73df;
          margin-bottom: 0.25rem;
          display: flex;
          align-items: center;
        }
        
        .ad-detail-value {
          color: #5a5c69;
        }
        
        .ad-preview {
          border: 1px solid #e3e6f0;
          border-radius: 0.35rem;
        }
      `}</style>
    </AdminLayout>
  );
}
