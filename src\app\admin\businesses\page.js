'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import AdminLayout from '@/layouts/AdminLayout';
import { useAuth } from '@/hooks/useAuth';

export default function AdminBusinessesPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [businesses, setBusinesses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [verificationFilter, setVerificationFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedBusiness, setSelectedBusiness] = useState(null);
  const [categories, setCategories] = useState([]);
  
  // Redirect if user is not logged in or not an admin
  useEffect(() => {
    if (!authLoading && (!user || (user.role !== 'admin' && user.role !== 'super_admin'))) {
      router.push('/login?redirect=/admin/businesses');
    }
  }, [user, authLoading, router]);
  
  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/categories');
        if (response.ok) {
          const data = await response.json();
          setCategories(data.categories);
        }
      } catch (err) {
        console.error('Error fetching categories:', err);
      }
    };
    
    fetchCategories();
  }, []);
  
  // Fetch businesses
  useEffect(() => {
    const fetchBusinesses = async () => {
      if (!user) return;
      
      setLoading(true);
      setError(null);
      
      try {
        // Build query parameters
        const queryParams = new URLSearchParams();
        queryParams.append('page', currentPage);
        queryParams.append('limit', 10);
        
        if (searchTerm) {
          queryParams.append('search', searchTerm);
        }
        
        if (categoryFilter !== 'all') {
          queryParams.append('category', categoryFilter);
        }
        
        if (verificationFilter !== 'all') {
          queryParams.append('kybVerified', verificationFilter);
        }
        
        // Fetch businesses
        const response = await fetch(`/api/businesses?${queryParams.toString()}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch businesses');
        }
        
        const data = await response.json();
        setBusinesses(data.businesses);
        setTotalPages(data.pagination.pages);
        
      } catch (err) {
        console.error('Error fetching businesses:', err);
        setError('Failed to load businesses. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    if (user && (user.role === 'admin' || user.role === 'super_admin')) {
      fetchBusinesses();
    }
  }, [user, searchTerm, categoryFilter, verificationFilter, currentPage]);
  
  // Handle business selection
  const handleBusinessSelect = (business) => {
    setSelectedBusiness(business);
  };
  
  // Handle business deletion
  const handleDeleteBusiness = async (businessId) => {
    if (!window.confirm('Are you sure you want to delete this business? This action cannot be undone.')) {
      return;
    }
    
    try {
      const response = await fetch(`/api/businesses/${businessId}`, {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete business');
      }
      
      // Remove business from state
      setBusinesses(businesses.filter(business => business._id !== businessId));
      
      // Reset selected business if it was deleted
      if (selectedBusiness && selectedBusiness._id === businessId) {
        setSelectedBusiness(null);
      }
      
      alert('Business deleted successfully');
    } catch (err) {
      console.error('Error deleting business:', err);
      alert('Failed to delete business. Please try again.');
    }
  };
  
  // Handle business verification status change
  const handleVerificationChange = async (businessId, status) => {
    try {
      const response = await fetch(`/api/verification/kyb/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          businessId,
          action: status === 'verified' ? 'approve' : 'reject',
          rejectionReason: status === 'rejected' ? 'Rejected by admin' : undefined
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to update verification status');
      }
      
      // Update business in state
      setBusinesses(businesses.map(business => 
        business._id === businessId 
          ? { ...business, kybVerified: status } 
          : business
      ));
      
      // Update selected business if it was updated
      if (selectedBusiness && selectedBusiness._id === businessId) {
        setSelectedBusiness({
          ...selectedBusiness,
          kybVerified: status
        });
      }
      
      alert(`Business ${status === 'verified' ? 'approved' : 'rejected'} successfully`);
    } catch (err) {
      console.error('Error updating verification status:', err);
      alert('Failed to update verification status. Please try again.');
    }
  };
  
  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };
  
  if (authLoading || loading) {
    return (
      <AdminLayout>
        <div className="text-center py-5">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Loading businesses...</p>
        </div>
      </AdminLayout>
    );
  }
  
  if (error) {
    return (
      <AdminLayout>
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      </AdminLayout>
    );
  }
  
  // If user is not logged in or not an admin, show nothing (will redirect)
  if (!user || (user.role !== 'admin' && user.role !== 'super_admin')) {
    return null;
  }
  
  return (
    <AdminLayout>
      <div className="admin-businesses">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h1 className="h3 mb-0 text-gray-800">Business Management</h1>
          <Link href="/admin/businesses/new" className="btn btn-primary">
            <i className="fas fa-plus me-2"></i>Add New Business
          </Link>
        </div>
        
        {/* Filters */}
        <div className="card shadow mb-4">
          <div className="card-header py-3">
            <h6 className="m-0 font-weight-bold text-primary">Filters</h6>
          </div>
          <div className="card-body">
            <div className="row">
              <div className="col-md-4 mb-3">
                <div className="input-group">
                  <input 
                    type="text" 
                    className="form-control" 
                    placeholder="Search businesses" 
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && setCurrentPage(1)}
                  />
                  <button 
                    className="btn btn-outline-secondary" 
                    type="button"
                    onClick={() => {
                      setSearchTerm('');
                      setCurrentPage(1);
                    }}
                  >
                    <i className="fas fa-times"></i>
                  </button>
                </div>
              </div>
              <div className="col-md-4 mb-3">
                <select 
                  className="form-select" 
                  value={categoryFilter}
                  onChange={(e) => {
                    setCategoryFilter(e.target.value);
                    setCurrentPage(1);
                  }}
                >
                  <option value="all">All Categories</option>
                  {categories.map(category => (
                    <option key={category._id} value={category.name}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
              <div className="col-md-4 mb-3">
                <select 
                  className="form-select" 
                  value={verificationFilter}
                  onChange={(e) => {
                    setVerificationFilter(e.target.value);
                    setCurrentPage(1);
                  }}
                >
                  <option value="all">All Verification Status</option>
                  <option value="verified">Verified</option>
                  <option value="pending">Pending</option>
                  <option value="rejected">Rejected</option>
                  <option value="unverified">Unverified</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        
        <div className="row">
          {/* Businesses List */}
          <div className="col-xl-8 col-lg-7">
            <div className="card shadow mb-4">
              <div className="card-header py-3">
                <h6 className="m-0 font-weight-bold text-primary">Businesses</h6>
              </div>
              <div className="card-body">
                {businesses.length === 0 ? (
                  <div className="text-center py-5">
                    <i className="fas fa-building fa-3x text-gray-300 mb-3"></i>
                    <p>No businesses found. {searchTerm || categoryFilter !== 'all' || verificationFilter !== 'all' ? 'Try adjusting your filters.' : ''}</p>
                  </div>
                ) : (
                  <div className="table-responsive">
                    <table className="table table-bordered" width="100%" cellSpacing="0">
                      <thead>
                        <tr>
                          <th>Business</th>
                          <th>Category</th>
                          <th>Owner</th>
                          <th>Verification</th>
                          <th>Created</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {businesses.map((business) => (
                          <tr key={business._id} className={selectedBusiness?._id === business._id ? 'table-active' : ''}>
                            <td>
                              <div className="d-flex align-items-center">
                                <div className="business-img me-2">
                                  <img 
                                    src={business.images && business.images.length > 0 
                                      ? business.images[0].url 
                                      : '/assets/img/placeholder-1.jpg'} 
                                    className="img-fluid rounded" 
                                    alt={business.name}
                                    width="40"
                                    height="40"
                                    style={{ objectFit: 'cover' }}
                                  />
                                </div>
                                <div>
                                  <h6 className="mb-0 ft-medium">
                                    <Link href={`/businesses/${business._id}`} className="text-dark">
                                      {business.name}
                                    </Link>
                                  </h6>
                                  <span className="text-muted small">
                                    {business.city ? `${business.city}, ${business.state || ''}` : 'No location'}
                                  </span>
                                </div>
                              </div>
                            </td>
                            <td>{business.category}</td>
                            <td>{business.ownerName || 'Unknown'}</td>
                            <td>
                              <span className={`badge ${
                                business.kybVerified === 'verified' ? 'bg-success' : 
                                business.kybVerified === 'rejected' ? 'bg-danger' : 
                                business.kybVerified === 'pending' ? 'bg-warning text-dark' : 
                                'bg-secondary'
                              }`}>
                                {business.kybVerified === 'verified' ? 'Verified' : 
                                 business.kybVerified === 'rejected' ? 'Rejected' : 
                                 business.kybVerified === 'pending' ? 'Pending' : 
                                 'Unverified'}
                              </span>
                            </td>
                            <td>{formatDate(business.createdAt)}</td>
                            <td>
                              <div className="btn-group">
                                <button 
                                  className="btn btn-sm btn-info"
                                  onClick={() => handleBusinessSelect(business)}
                                >
                                  <i className="fas fa-eye"></i>
                                </button>
                                <Link 
                                  href={`/admin/businesses/edit/${business._id}`} 
                                  className="btn btn-sm btn-primary"
                                >
                                  <i className="fas fa-edit"></i>
                                </Link>
                                <button 
                                  className="btn btn-sm btn-danger"
                                  onClick={() => handleDeleteBusiness(business._id)}
                                >
                                  <i className="fas fa-trash"></i>
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
                
                {/* Pagination */}
                {totalPages > 1 && (
                  <nav aria-label="Page navigation">
                    <ul className="pagination justify-content-center mt-4">
                      <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                        <button 
                          className="page-link" 
                          onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                        >
                          Previous
                        </button>
                      </li>
                      
                      {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                        <li key={page} className={`page-item ${currentPage === page ? 'active' : ''}`}>
                          <button 
                            className="page-link" 
                            onClick={() => setCurrentPage(page)}
                          >
                            {page}
                          </button>
                        </li>
                      ))}
                      
                      <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>
                        <button 
                          className="page-link" 
                          onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                        >
                          Next
                        </button>
                      </li>
                    </ul>
                  </nav>
                )}
              </div>
            </div>
          </div>
          
          {/* Business Details */}
          <div className="col-xl-4 col-lg-5">
            <div className="card shadow mb-4">
              <div className="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 className="m-0 font-weight-bold text-primary">
                  {selectedBusiness ? 'Business Details' : 'Select a Business'}
                </h6>
                {selectedBusiness && (
                  <Link 
                    href={`/admin/businesses/edit/${selectedBusiness._id}`} 
                    className="btn btn-sm btn-primary"
                  >
                    <i className="fas fa-edit me-1"></i>Edit
                  </Link>
                )}
              </div>
              <div className="card-body">
                {selectedBusiness ? (
                  <div className="business-details">
                    <div className="text-center mb-4">
                      <img 
                        src={selectedBusiness.images && selectedBusiness.images.length > 0 
                          ? selectedBusiness.images[0].url 
                          : '/assets/img/placeholder-1.jpg'} 
                        alt={selectedBusiness.name} 
                        className="img-fluid rounded" 
                        style={{ maxHeight: '200px', objectFit: 'cover' }}
                      />
                      <h5 className="mt-3">{selectedBusiness.name}</h5>
                      <span className="badge bg-primary">{selectedBusiness.category}</span>
                    </div>
                    
                    <div className="business-info">
                      <div className="info-item">
                        <div className="info-label">Owner</div>
                        <div className="info-value">{selectedBusiness.ownerName || 'Unknown'}</div>
                      </div>
                      
                      <div className="info-item">
                        <div className="info-label">Location</div>
                        <div className="info-value">
                          {[
                            selectedBusiness.address,
                            selectedBusiness.city,
                            selectedBusiness.state,
                            selectedBusiness.zipCode,
                            selectedBusiness.country
                          ].filter(Boolean).join(', ')}
                        </div>
                      </div>
                      
                      <div className="info-item">
                        <div className="info-label">Contact</div>
                        <div className="info-value">
                          {selectedBusiness.phone || 'No phone'}<br />
                          {selectedBusiness.email || 'No email'}
                        </div>
                      </div>
                      
                      <div className="info-item">
                        <div className="info-label">Verification Status</div>
                        <div className="info-value">
                          <span className={`badge ${
                            selectedBusiness.kybVerified === 'verified' ? 'bg-success' : 
                            selectedBusiness.kybVerified === 'rejected' ? 'bg-danger' : 
                            selectedBusiness.kybVerified === 'pending' ? 'bg-warning text-dark' : 
                            'bg-secondary'
                          }`}>
                            {selectedBusiness.kybVerified === 'verified' ? 'Verified' : 
                             selectedBusiness.kybVerified === 'rejected' ? 'Rejected' : 
                             selectedBusiness.kybVerified === 'pending' ? 'Pending' : 
                             'Unverified'}
                          </span>
                        </div>
                      </div>
                      
                      <div className="info-item">
                        <div className="info-label">Booking Enabled</div>
                        <div className="info-value">
                          <span className={`badge ${selectedBusiness.bookingEnabled ? 'bg-success' : 'bg-secondary'}`}>
                            {selectedBusiness.bookingEnabled ? 'Enabled' : 'Disabled'}
                          </span>
                        </div>
                      </div>
                      
                      <div className="info-item">
                        <div className="info-label">Created</div>
                        <div className="info-value">{formatDate(selectedBusiness.createdAt)}</div>
                      </div>
                    </div>
                    
                    <div className="business-actions mt-4">
                      <div className="d-grid gap-2">
                        <Link 
                          href={`/businesses/${selectedBusiness._id}`} 
                          className="btn btn-info"
                          target="_blank"
                        >
                          <i className="fas fa-eye me-1"></i>View Public Page
                        </Link>
                        
                        {selectedBusiness.kybVerified === 'pending' && (
                          <div className="d-flex gap-2">
                            <button 
                              className="btn btn-success flex-grow-1"
                              onClick={() => handleVerificationChange(selectedBusiness._id, 'verified')}
                            >
                              <i className="fas fa-check me-1"></i>Approve
                            </button>
                            <button 
                              className="btn btn-danger flex-grow-1"
                              onClick={() => handleVerificationChange(selectedBusiness._id, 'rejected')}
                            >
                              <i className="fas fa-times me-1"></i>Reject
                            </button>
                          </div>
                        )}
                        
                        {selectedBusiness.kybVerified === 'rejected' && (
                          <button 
                            className="btn btn-success"
                            onClick={() => handleVerificationChange(selectedBusiness._id, 'verified')}
                          >
                            <i className="fas fa-check me-1"></i>Approve Verification
                          </button>
                        )}
                        
                        {selectedBusiness.kybVerified === 'unverified' && (
                          <button 
                            className="btn btn-success"
                            onClick={() => handleVerificationChange(selectedBusiness._id, 'verified')}
                          >
                            <i className="fas fa-check me-1"></i>Approve Without Documents
                          </button>
                        )}
                        
                        <Link 
                          href={`/admin/verification/kyb?businessId=${selectedBusiness._id}`} 
                          className="btn btn-primary"
                        >
                          <i className="fas fa-id-card me-1"></i>View Verification Documents
                        </Link>
                        
                        <button 
                          className="btn btn-danger"
                          onClick={() => handleDeleteBusiness(selectedBusiness._id)}
                        >
                          <i className="fas fa-trash me-1"></i>Delete Business
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-5">
                    <i className="fas fa-building fa-4x text-gray-300 mb-3"></i>
                    <p>Select a business to view details</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <style jsx>{`
        .business-details .info-item {
          margin-bottom: 1rem;
          border-bottom: 1px solid #e3e6f0;
          padding-bottom: 0.5rem;
        }
        
        .business-details .info-label {
          font-size: 0.8rem;
          color: #858796;
          text-transform: uppercase;
          font-weight: 600;
        }
        
        .business-details .info-value {
          font-size: 1rem;
          color: #5a5c69;
        }
      `}</style>
    </AdminLayout>
  );
}
