'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AdminLayout from '@/layouts/AdminLayout';
import { useAuth } from '@/hooks/useAuth';
import dynamic from 'next/dynamic';

// Import charts dynamically to avoid SSR issues
const Chart = dynamic(() => import('react-apexcharts'), { ssr: false });

export default function AdminAnalyticsPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalBusinesses: 0,
    totalBookings: 0,
    totalRevenue: 0,
    pendingVerifications: 0
  });
  const [timeRange, setTimeRange] = useState('month');
  const [chartData, setChartData] = useState({
    users: [],
    businesses: [],
    bookings: [],
    revenue: []
  });
  
  // Redirect if user is not logged in or not an admin
  useEffect(() => {
    if (!authLoading && (!user || (user.role !== 'admin' && user.role !== 'super_admin'))) {
      router.push('/login?redirect=/admin/analytics');
    }
  }, [user, authLoading, router]);
  
  // Fetch analytics data
  useEffect(() => {
    const fetchAnalytics = async () => {
      if (!user) return;
      
      setLoading(true);
      setError(null);
      
      try {
        // For now, we'll use mock data
        // In a real implementation, you would fetch this data from your API
        
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Mock stats data
        setStats({
          totalUsers: 256,
          totalBusinesses: 124,
          totalBookings: 89,
          totalRevenue: 12450.75,
          pendingVerifications: 15
        });
        
        // Generate mock chart data based on time range
        generateMockChartData(timeRange);
        
      } catch (err) {
        console.error('Error fetching analytics:', err);
        setError('Failed to load analytics data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    if (user && (user.role === 'admin' || user.role === 'super_admin')) {
      fetchAnalytics();
    }
  }, [user, timeRange]);
  
  // Generate mock chart data
  const generateMockChartData = (range) => {
    let labels = [];
    let userSeries = [];
    let businessSeries = [];
    let bookingSeries = [];
    let revenueSeries = [];
    
    if (range === 'week') {
      labels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
      userSeries = [5, 7, 3, 9, 4, 2, 6];
      businessSeries = [2, 3, 1, 4, 2, 1, 3];
      bookingSeries = [3, 5, 2, 7, 4, 6, 8];
      revenueSeries = [150, 230, 120, 350, 180, 270, 320];
    } else if (range === 'month') {
      labels = Array.from({ length: 30 }, (_, i) => (i + 1).toString());
      userSeries = Array.from({ length: 30 }, () => Math.floor(Math.random() * 10) + 1);
      businessSeries = Array.from({ length: 30 }, () => Math.floor(Math.random() * 5) + 1);
      bookingSeries = Array.from({ length: 30 }, () => Math.floor(Math.random() * 15) + 1);
      revenueSeries = Array.from({ length: 30 }, () => Math.floor(Math.random() * 500) + 50);
    } else if (range === 'year') {
      labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      userSeries = [25, 32, 18, 40, 35, 28, 45, 50, 38, 42, 48, 55];
      businessSeries = [12, 15, 8, 18, 16, 14, 20, 22, 17, 19, 21, 25];
      bookingSeries = [35, 42, 28, 50, 45, 38, 55, 60, 48, 52, 58, 65];
      revenueSeries = [1500, 1800, 1200, 2200, 1900, 1600, 2500, 2800, 2000, 2300, 2600, 3000];
    }
    
    setChartData({
      users: {
        options: {
          chart: {
            id: 'users-chart',
            toolbar: {
              show: false
            }
          },
          xaxis: {
            categories: labels
          },
          colors: ['#4e73df'],
          title: {
            text: 'New Users',
            align: 'left'
          }
        },
        series: [
          {
            name: 'New Users',
            data: userSeries
          }
        ]
      },
      businesses: {
        options: {
          chart: {
            id: 'businesses-chart',
            toolbar: {
              show: false
            }
          },
          xaxis: {
            categories: labels
          },
          colors: ['#1cc88a'],
          title: {
            text: 'New Businesses',
            align: 'left'
          }
        },
        series: [
          {
            name: 'New Businesses',
            data: businessSeries
          }
        ]
      },
      bookings: {
        options: {
          chart: {
            id: 'bookings-chart',
            toolbar: {
              show: false
            }
          },
          xaxis: {
            categories: labels
          },
          colors: ['#36b9cc'],
          title: {
            text: 'Bookings',
            align: 'left'
          }
        },
        series: [
          {
            name: 'Bookings',
            data: bookingSeries
          }
        ]
      },
      revenue: {
        options: {
          chart: {
            id: 'revenue-chart',
            toolbar: {
              show: false
            }
          },
          xaxis: {
            categories: labels
          },
          colors: ['#f6c23e'],
          title: {
            text: 'Revenue ($)',
            align: 'left'
          },
          dataLabels: {
            enabled: false
          }
        },
        series: [
          {
            name: 'Revenue',
            data: revenueSeries
          }
        ]
      }
    });
  };
  
  // Generate pie chart data for business categories
  const categoryPieChartData = {
    options: {
      chart: {
        id: 'category-pie-chart'
      },
      labels: ['Restaurants', 'Hotels', 'Retail', 'Services', 'Entertainment', 'Other'],
      colors: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', '#858796'],
      title: {
        text: 'Businesses by Category',
        align: 'left'
      },
      legend: {
        position: 'bottom'
      }
    },
    series: [35, 25, 15, 10, 8, 7]
  };
  
  // Generate pie chart data for verification status
  const verificationPieChartData = {
    options: {
      chart: {
        id: 'verification-pie-chart'
      },
      labels: ['Verified', 'Pending', 'Rejected', 'Unverified'],
      colors: ['#1cc88a', '#f6c23e', '#e74a3b', '#858796'],
      title: {
        text: 'Verification Status',
        align: 'left'
      },
      legend: {
        position: 'bottom'
      }
    },
    series: [60, 15, 10, 15]
  };
  
  if (authLoading || loading) {
    return (
      <AdminLayout>
        <div className="text-center py-5">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Loading analytics data...</p>
        </div>
      </AdminLayout>
    );
  }
  
  if (error) {
    return (
      <AdminLayout>
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      </AdminLayout>
    );
  }
  
  // If user is not logged in or not an admin, show nothing (will redirect)
  if (!user || (user.role !== 'admin' && user.role !== 'super_admin')) {
    return null;
  }
  
  return (
    <AdminLayout>
      <div className="admin-analytics">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h1 className="h3 mb-0 text-gray-800">Analytics & Reporting</h1>
          <div className="btn-group">
            <button 
              type="button" 
              className={`btn ${timeRange === 'week' ? 'btn-primary' : 'btn-outline-primary'}`}
              onClick={() => setTimeRange('week')}
            >
              Week
            </button>
            <button 
              type="button" 
              className={`btn ${timeRange === 'month' ? 'btn-primary' : 'btn-outline-primary'}`}
              onClick={() => setTimeRange('month')}
            >
              Month
            </button>
            <button 
              type="button" 
              className={`btn ${timeRange === 'year' ? 'btn-primary' : 'btn-outline-primary'}`}
              onClick={() => setTimeRange('year')}
            >
              Year
            </button>
          </div>
        </div>
        
        {/* Stats Cards */}
        <div className="row mb-4">
          <div className="col-xl-3 col-md-6 mb-4">
            <div className="card border-left-primary shadow h-100 py-2">
              <div className="card-body">
                <div className="row no-gutters align-items-center">
                  <div className="col mr-2">
                    <div className="text-xs font-weight-bold text-primary text-uppercase mb-1">
                      Total Users
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">{stats.totalUsers}</div>
                  </div>
                  <div className="col-auto">
                    <i className="fas fa-users fa-2x text-gray-300"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="col-xl-3 col-md-6 mb-4">
            <div className="card border-left-success shadow h-100 py-2">
              <div className="card-body">
                <div className="row no-gutters align-items-center">
                  <div className="col mr-2">
                    <div className="text-xs font-weight-bold text-success text-uppercase mb-1">
                      Total Businesses
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">{stats.totalBusinesses}</div>
                  </div>
                  <div className="col-auto">
                    <i className="fas fa-building fa-2x text-gray-300"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="col-xl-3 col-md-6 mb-4">
            <div className="card border-left-info shadow h-100 py-2">
              <div className="card-body">
                <div className="row no-gutters align-items-center">
                  <div className="col mr-2">
                    <div className="text-xs font-weight-bold text-info text-uppercase mb-1">
                      Total Bookings
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">{stats.totalBookings}</div>
                  </div>
                  <div className="col-auto">
                    <i className="fas fa-calendar-check fa-2x text-gray-300"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="col-xl-3 col-md-6 mb-4">
            <div className="card border-left-warning shadow h-100 py-2">
              <div className="card-body">
                <div className="row no-gutters align-items-center">
                  <div className="col mr-2">
                    <div className="text-xs font-weight-bold text-warning text-uppercase mb-1">
                      Total Revenue
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">${stats.totalRevenue.toFixed(2)}</div>
                  </div>
                  <div className="col-auto">
                    <i className="fas fa-dollar-sign fa-2x text-gray-300"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Line Charts */}
        <div className="row">
          <div className="col-xl-6 col-lg-6 mb-4">
            <div className="card shadow mb-4">
              <div className="card-body">
                {typeof window !== 'undefined' && (
                  <Chart
                    options={chartData.users?.options || {}}
                    series={chartData.users?.series || []}
                    type="line"
                    height={300}
                  />
                )}
              </div>
            </div>
          </div>
          
          <div className="col-xl-6 col-lg-6 mb-4">
            <div className="card shadow mb-4">
              <div className="card-body">
                {typeof window !== 'undefined' && (
                  <Chart
                    options={chartData.businesses?.options || {}}
                    series={chartData.businesses?.series || []}
                    type="line"
                    height={300}
                  />
                )}
              </div>
            </div>
          </div>
          
          <div className="col-xl-6 col-lg-6 mb-4">
            <div className="card shadow mb-4">
              <div className="card-body">
                {typeof window !== 'undefined' && (
                  <Chart
                    options={chartData.bookings?.options || {}}
                    series={chartData.bookings?.series || []}
                    type="line"
                    height={300}
                  />
                )}
              </div>
            </div>
          </div>
          
          <div className="col-xl-6 col-lg-6 mb-4">
            <div className="card shadow mb-4">
              <div className="card-body">
                {typeof window !== 'undefined' && (
                  <Chart
                    options={chartData.revenue?.options || {}}
                    series={chartData.revenue?.series || []}
                    type="area"
                    height={300}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
        
        {/* Pie Charts */}
        <div className="row">
          <div className="col-xl-6 col-lg-6 mb-4">
            <div className="card shadow mb-4">
              <div className="card-body">
                {typeof window !== 'undefined' && (
                  <Chart
                    options={categoryPieChartData.options}
                    series={categoryPieChartData.series}
                    type="pie"
                    height={350}
                  />
                )}
              </div>
            </div>
          </div>
          
          <div className="col-xl-6 col-lg-6 mb-4">
            <div className="card shadow mb-4">
              <div className="card-body">
                {typeof window !== 'undefined' && (
                  <Chart
                    options={verificationPieChartData.options}
                    series={verificationPieChartData.series}
                    type="pie"
                    height={350}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
        
        {/* Reports Section */}
        <div className="row">
          <div className="col-12">
            <div className="card shadow mb-4">
              <div className="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 className="m-0 font-weight-bold text-primary">Reports</h6>
                <div className="dropdown">
                  <button 
                    className="btn btn-primary dropdown-toggle" 
                    type="button" 
                    id="dropdownMenuButton" 
                    data-bs-toggle="dropdown" 
                    aria-expanded="false"
                  >
                    Export Report
                  </button>
                  <ul className="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    <li><a className="dropdown-item" href="#">Export as PDF</a></li>
                    <li><a className="dropdown-item" href="#">Export as CSV</a></li>
                    <li><a className="dropdown-item" href="#">Export as Excel</a></li>
                  </ul>
                </div>
              </div>
              <div className="card-body">
                <div className="table-responsive">
                  <table className="table table-bordered" width="100%" cellSpacing="0">
                    <thead>
                      <tr>
                        <th>Report Name</th>
                        <th>Description</th>
                        <th>Last Generated</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>User Growth Report</td>
                        <td>Detailed report on user growth and engagement</td>
                        <td>2023-06-15</td>
                        <td>
                          <button className="btn btn-sm btn-primary me-2">Generate</button>
                          <button className="btn btn-sm btn-info">View</button>
                        </td>
                      </tr>
                      <tr>
                        <td>Business Performance Report</td>
                        <td>Analysis of business listings and performance</td>
                        <td>2023-06-14</td>
                        <td>
                          <button className="btn btn-sm btn-primary me-2">Generate</button>
                          <button className="btn btn-sm btn-info">View</button>
                        </td>
                      </tr>
                      <tr>
                        <td>Booking Analytics Report</td>
                        <td>Detailed booking statistics and trends</td>
                        <td>2023-06-13</td>
                        <td>
                          <button className="btn btn-sm btn-primary me-2">Generate</button>
                          <button className="btn btn-sm btn-info">View</button>
                        </td>
                      </tr>
                      <tr>
                        <td>Revenue Report</td>
                        <td>Financial analysis and revenue breakdown</td>
                        <td>2023-06-12</td>
                        <td>
                          <button className="btn btn-sm btn-primary me-2">Generate</button>
                          <button className="btn btn-sm btn-info">View</button>
                        </td>
                      </tr>
                      <tr>
                        <td>Verification Status Report</td>
                        <td>Overview of business verification statuses</td>
                        <td>2023-06-11</td>
                        <td>
                          <button className="btn btn-sm btn-primary me-2">Generate</button>
                          <button className="btn btn-sm btn-info">View</button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <style jsx>{`
        .border-left-primary {
          border-left: 0.25rem solid #4e73df !important;
        }
        
        .border-left-success {
          border-left: 0.25rem solid #1cc88a !important;
        }
        
        .border-left-info {
          border-left: 0.25rem solid #36b9cc !important;
        }
        
        .border-left-warning {
          border-left: 0.25rem solid #f6c23e !important;
        }
        
        .text-xs {
          font-size: 0.7rem;
        }
        
        .text-gray-300 {
          color: #dddfeb !important;
        }
        
        .text-gray-800 {
          color: #5a5c69 !important;
        }
      `}</style>
    </AdminLayout>
  );
}
