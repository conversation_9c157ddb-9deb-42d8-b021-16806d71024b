// include-header.js

/**
 * This script loads the header.html content into any element with the id "header-container"
 * It should be included in all pages that need the header
 */
document.addEventListener('DOMContentLoaded', async () => {
    const headerContainer = document.getElementById('header-container');
    
    if (headerContainer) {
        try {
            const response = await fetch('/header.html');
            if (!response.ok) {
                throw new Error(`Failed to load header: ${response.status}`);
            }
            
            const html = await response.text();
            headerContainer.innerHTML = html;
            
            // Load the header.js script after the HTML is inserted
            const script = document.createElement('script');
            script.type = 'module';
            script.src = '/assets/js/header.js';
            document.body.appendChild(script);
            
        } catch (error) {
            console.error('Error loading header:', error);
            headerContainer.innerHTML = '<p>Error loading header. Please refresh the page.</p>';
        }
    }
});
