// Define exports and module.exports if they don't exist
if (typeof exports === 'undefined') {
  window.exports = {};
}
if (typeof module === 'undefined') {
  window.module = { exports: {} };
}

// Add error handler for script loading
window.addEventListener('error', function(event) {
  // Check if this is a vendor.js error
  if (event.filename && event.filename.includes('vendor.js')) {
    console.warn('Caught vendor.js error:', event.message);
    
    // Define exports and module again
    window.exports = window.exports || {};
    window.module = window.module || { exports: {} };
    
    // Prevent the error from bubbling up
    event.preventDefault();
    
    // Try to reload the vendor.js file
    const script = document.createElement('script');
    script.src = event.filename;
    script.async = true;
    document.head.appendChild(script);
  }
}, true);
