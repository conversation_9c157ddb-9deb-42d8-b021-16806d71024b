'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import AdminLayout from '@/layouts/AdminLayout';
import { useAuth } from '@/hooks/useAuth';

export default function BlogCategories() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    description: ''
  });
  const [formError, setFormError] = useState(null);
  const [formSuccess, setFormSuccess] = useState(false);
  const [formLoading, setFormLoading] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [editId, setEditId] = useState(null);
  
  // Redirect if user is not logged in or not an admin
  useEffect(() => {
    if (!authLoading && (!user || (user.role !== 'admin' && user.role !== 'super_admin'))) {
      router.push('/login?redirect=/admin/blog/categories');
    }
  }, [user, authLoading, router]);
  
  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      if (!user) return;
      
      setLoading(true);
      setError(null);
      
      try {
        const response = await fetch('/api/blog/categories');
        
        if (!response.ok) {
          throw new Error('Failed to fetch categories');
        }
        
        const data = await response.json();
        setCategories(data.categories);
        
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError('Failed to load categories. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    if (user && (user.role === 'admin' || user.role === 'super_admin')) {
      fetchCategories();
    }
  }, [user]);
  
  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Generate slug from name if name changes and not in edit mode
    if (name === 'name' && !editMode) {
      setFormData(prev => ({
        ...prev,
        slug: createSlug(value)
      }));
    }
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form
    if (!formData.name) {
      setFormError('Category name is required');
      return;
    }
    
    setFormLoading(true);
    setFormError(null);
    setFormSuccess(false);
    
    try {
      let response;
      
      if (editMode) {
        // Update existing category
        response = await fetch(`/api/blog/categories/${editId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(formData)
        });
      } else {
        // Create new category
        response = await fetch('/api/blog/categories', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(formData)
        });
      }
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save category');
      }
      
      const data = await response.json();
      
      // Update categories list
      if (editMode) {
        setCategories(categories.map(cat => 
          cat._id === editId ? data.category : cat
        ));
      } else {
        setCategories([...categories, data.category]);
      }
      
      // Reset form
      setFormData({
        name: '',
        slug: '',
        description: ''
      });
      setEditMode(false);
      setEditId(null);
      setFormSuccess(true);
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setFormSuccess(false);
      }, 3000);
      
    } catch (err) {
      console.error('Error saving category:', err);
      setFormError(err.message || 'An error occurred while saving the category');
    } finally {
      setFormLoading(false);
    }
  };
  
  // Handle edit category
  const handleEditCategory = (category) => {
    setFormData({
      name: category.name,
      slug: category.slug,
      description: category.description || ''
    });
    setEditMode(true);
    setEditId(category._id);
    setFormError(null);
    setFormSuccess(false);
    
    // Scroll to form
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };
  
  // Handle delete category
  const handleDeleteCategory = async (categoryId) => {
    if (!window.confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
      return;
    }
    
    try {
      const response = await fetch(`/api/blog/categories/${categoryId}`, {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete category');
      }
      
      // Remove category from state
      setCategories(categories.filter(cat => cat._id !== categoryId));
      
      // Reset form if editing the deleted category
      if (editId === categoryId) {
        setFormData({
          name: '',
          slug: '',
          description: ''
        });
        setEditMode(false);
        setEditId(null);
      }
      
      alert('Category deleted successfully');
    } catch (err) {
      console.error('Error deleting category:', err);
      alert('Failed to delete category. Please try again.');
    }
  };
  
  // Cancel edit
  const handleCancelEdit = () => {
    setFormData({
      name: '',
      slug: '',
      description: ''
    });
    setEditMode(false);
    setEditId(null);
    setFormError(null);
    setFormSuccess(false);
  };
  
  if (authLoading || loading) {
    return (
      <AdminLayout>
        <div className="text-center py-5">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Loading categories...</p>
        </div>
      </AdminLayout>
    );
  }
  
  if (error) {
    return (
      <AdminLayout>
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      </AdminLayout>
    );
  }
  
  // If user is not logged in or not an admin, show nothing (will redirect)
  if (!user || (user.role !== 'admin' && user.role !== 'super_admin')) {
    return null;
  }
  
  return (
    <AdminLayout>
      <div className="admin-blog-categories">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h1 className="h3 mb-0 text-gray-800">Blog Categories</h1>
          <Link href="/admin/blog" className="btn btn-secondary">
            <i className="fas fa-arrow-left me-2"></i>Back to Blog
          </Link>
        </div>
        
        <div className="row">
          <div className="col-lg-5">
            {/* Category Form */}
            <div className="card shadow mb-4">
              <div className="card-header py-3">
                <h6 className="m-0 font-weight-bold text-primary">
                  {editMode ? 'Edit Category' : 'Add New Category'}
                </h6>
              </div>
              <div className="card-body">
                {formError && (
                  <div className="alert alert-danger" role="alert">
                    {formError}
                  </div>
                )}
                
                {formSuccess && (
                  <div className="alert alert-success" role="alert">
                    Category {editMode ? 'updated' : 'created'} successfully!
                  </div>
                )}
                
                <form onSubmit={handleSubmit}>
                  <div className="mb-3">
                    <label htmlFor="name" className="form-label">Name*</label>
                    <input
                      type="text"
                      className="form-control"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <div className="mb-3">
                    <label htmlFor="slug" className="form-label">Slug*</label>
                    <input
                      type="text"
                      className="form-control"
                      id="slug"
                      name="slug"
                      value={formData.slug}
                      onChange={handleInputChange}
                      required
                    />
                    <div className="form-text">
                      URL-friendly version of the name. Automatically generated but can be edited.
                    </div>
                  </div>
                  
                  <div className="mb-3">
                    <label htmlFor="description" className="form-label">Description</label>
                    <textarea
                      className="form-control"
                      id="description"
                      name="description"
                      rows="3"
                      value={formData.description}
                      onChange={handleInputChange}
                    ></textarea>
                  </div>
                  
                  <div className="d-flex justify-content-between">
                    {editMode && (
                      <button
                        type="button"
                        className="btn btn-secondary"
                        onClick={handleCancelEdit}
                      >
                        Cancel
                      </button>
                    )}
                    <button
                      type="submit"
                      className="btn btn-primary"
                      disabled={formLoading}
                    >
                      {formLoading ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                          Saving...
                        </>
                      ) : editMode ? 'Update Category' : 'Add Category'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
          
          <div className="col-lg-7">
            {/* Categories List */}
            <div className="card shadow mb-4">
              <div className="card-header py-3">
                <h6 className="m-0 font-weight-bold text-primary">Categories</h6>
              </div>
              <div className="card-body">
                {categories.length === 0 ? (
                  <div className="text-center py-5">
                    <i className="fas fa-folder fa-3x text-gray-300 mb-3"></i>
                    <p>No categories found.</p>
                  </div>
                ) : (
                  <div className="table-responsive">
                    <table className="table table-bordered" width="100%" cellSpacing="0">
                      <thead>
                        <tr>
                          <th>Name</th>
                          <th>Slug</th>
                          <th>Description</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {categories.map((category) => (
                          <tr key={category._id} className={editId === category._id ? 'table-active' : ''}>
                            <td>{category.name}</td>
                            <td>{category.slug}</td>
                            <td>{truncateText(category.description, 50)}</td>
                            <td>
                              <div className="btn-group">
                                <button
                                  className="btn btn-sm btn-primary"
                                  onClick={() => handleEditCategory(category)}
                                >
                                  <i className="fas fa-edit"></i>
                                </button>
                                <button
                                  className="btn btn-sm btn-danger"
                                  onClick={() => handleDeleteCategory(category._id)}
                                >
                                  <i className="fas fa-trash"></i>
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}

// Helper function to create a slug from name
function createSlug(name) {
  return name
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .trim();
}

// Helper function to truncate text
function truncateText(text, maxLength = 100) {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
}
