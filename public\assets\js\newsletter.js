// newsletter.js
class NewsletterManager {

  constructor() {
    this.setupSubscriptionForm();
    //optional, as this is only used in sign up page
    this.addNewsletterCheckbox();
  }

  setupSubscriptionForm() {
    const form = document.getElementById('newsletter-form');
    if (form) {
        form.addEventListener('submit', this.handleSubscription.bind(this));
    }
  }

  addNewsletterCheckbox() {
    const signupForm = document.getElementById('signup-form'); // ID of your signup form
    if (signupForm) {
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.id = 'newsletter-checkbox';
        checkbox.name = 'newsletterSubscribed'; // Important for form submission
        checkbox.checked = false;

        const label = document.createElement('label');
        label.htmlFor = 'newsletter-checkbox';
        label.textContent = 'Subscribe to our newsletter';

        signupForm.appendChild(checkbox);
        signupForm.appendChild(label);
    }
  }

  async handleSubscription(event) {
     event.preventDefault();
     const emailInput = event.target.querySelector('input[type="email"]') || event.target.querySelector('input[type="text"]');
     const email = emailInput.value;

    if (!this.isValidEmail(email)) {
        this.showMessage('Invalid email format.', 'error');
        return;
    }

    try {
        const response = await fetch('/api/newsletter/subscribe', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email: email })
        });

        const result = await response.json(); // Always parse JSON, even on error

        if (response.ok) {
            this.showMessage(result.message || 'Subscribed successfully!  Please check your email for confirmation.', 'success');
            emailInput.value = ''; // Clear the input
        } else {
          // Use the message from the server if available, otherwise a default message
            throw new Error(result.message || 'Failed to subscribe.');
        }
    } catch (error) {
        console.error('Subscription error:', error);
        this.showMessage(error.message, 'error'); // Display the error message
    }
  }

  isValidEmail(email) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  }

  showMessage(message, type) {
    const container = document.getElementById('newsletter-message') || document.body;
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
    alertDiv.role = 'alert';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    container.appendChild(alertDiv);
    // Automatically remove the alert after 5 seconds
    setTimeout(() => alertDiv.remove(), 5000);
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new NewsletterManager();
});
