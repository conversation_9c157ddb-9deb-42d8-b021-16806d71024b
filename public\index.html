<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finda - Find Anything You Need</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- Line Icons -->
    <link href="https://cdn.lineicons.com/3.0/lineicons.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/styles.css">
</head>
<body>
    <!-- Header Container - This is where the header will be loaded -->
    <div id="header-container"></div>

    <!-- Main Content -->
    <main>
        <!-- Hero Section with Search Bar -->
        <section class="hero-banner">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 col-md-10 mx-auto text-center">
                        <h1 class="mb-4">Find Anything You Need</h1>
                        <p class="lead mb-4">Your one-stop platform for finding businesses, services, and more.</p>

                        <!-- Search Bar Container -->
                        <div id="search-bar-container" class="mt-4"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Featured Listings Section -->
        <section class="py-5">
            <div class="container">
                <div class="row mb-4">
                    <div class="col-12">
                        <h2 class="section-title">Featured Listings</h2>
                    </div>
                </div>

                <!-- Business Listings Container -->
                <div id="business-listings-container" class="row"></div>
            </div>
        </section>
    </main>

    <!-- Footer Container - This is where the footer will be loaded -->
    <div id="footer-container"></div>

    <!-- Login Modal -->
    <div class="modal fade" id="login" tabindex="-1" aria-labelledby="loginLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="loginLabel">Login</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="login-form">
                        <div class="mb-3">
                            <label for="email" class="form-label">Email address</label>
                            <input type="email" class="form-control" id="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" required>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember">
                            <label class="form-check-label" for="remember">Remember me</label>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">Login</button>
                    </form>
                    <div class="mt-3 text-center">
                        <p>Don't have an account? <a href="register.html">Register</a></p>
                        <p><a href="forgot-password.html">Forgot Password?</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Include Header Script -->
    <script src="assets/js/include-header.js"></script>

    <!-- Include Footer Script -->
    <script src="assets/js/include-footer.js"></script>

    <!-- Business Card Component -->
    <script type="module" src="assets/js/components/business-card.js"></script>

    <!-- Search Bar Component -->
    <script type="module" src="assets/js/components/search-bar.js"></script>

    <!-- Custom Scripts -->
    <script type="module">
        // Login form handling
        document.getElementById('login-form')?.addEventListener('submit', async (e) => {
            e.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.message || 'Login failed');
                }

                const data = await response.json();
                localStorage.setItem('token', data.token);

                // Close the modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('login'));
                modal.hide();

                // Reload the page to update the header
                window.location.reload();

            } catch (error) {
                console.error('Login error:', error);
                alert(error.message);
            }
        });
    </script>
</body>
</html>
