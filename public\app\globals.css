@import "tailwindcss";
@import "../styles/messaging.css";
@import "../styles/notifications.css";
@import "../styles/dashboard.css";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #359e04;
  --primary-light: #4ac415;
  --primary-dark: #2a7c03;
  --primary-rgb: 53, 158, 4;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-mono: var(--font-roboto-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
}

/* Primary color utility classes */
.text-primary {
  color: var(--primary) !important;
}

.bg-primary {
  background-color: var(--primary) !important;
}

.border-primary {
  border-color: var(--primary) !important;
}

.theme-cl {
  color: var(--primary) !important;
}

.theme-bg {
  background-color: var(--primary) !important;
}

.theme-bg-light {
  background-color: rgba(var(--primary-rgb), 0.1) !important;
}

.btn-primary {
  background-color: var(--primary) !important;
  border-color: var(--primary) !important;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active {
  background-color: var(--primary-dark) !important;
  border-color: var(--primary-dark) !important;
}

.hover-theme:hover {
  background-color: var(--primary-dark) !important;
  border-color: var(--primary-dark) !important;
}

/* Image uploader overlay */
.image-uploader-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.image-uploader-overlay .card {
  width: 90%;
  max-width: 500px;
}

/* Make sure the editor container has position relative */
.position-relative {
  position: relative;
}
