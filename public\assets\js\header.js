// header.js

import { checkAuth, logout } from './auth.js'; // Import auth functions

/**
 * Initialize the header component with dynamic content based on authentication status
 */
document.addEventListener('DOMContentLoaded', () => {
    const headerActions = document.getElementById('header-actions'); // The container for login/logout/user info
    const dashboardLink = document.getElementById('dashboard-link'); // The dashboard link (to be shown/hidden)

    // Function to update the header based on authentication status
    function updateHeader(user) {
        if (user) {
            // User is logged in
            let dashboardUrl = 'user-dashboard.html'; // Default to customer dashboard
            if (user.role === 'business_owner') {
                dashboardUrl = 'dashboard-my-listings.html'; // For Business owners
            }
            else if (user.role === 'admin' || user.role === 'super_admin') {
                dashboardUrl = 'admin-dashboard.html'; // For Admin
            }
            else if (user.role === 'content_manager') {
                dashboardUrl = 'content-manager-dashboard.html'; // For content manager
            }

            // Construct the user menu HTML
            dashboardLink.innerHTML = `<a href="${dashboardUrl}">Dashboard</a>
            <ul class="nav-dropdown nav-submenu">
                <li><a href="${dashboardUrl}"><i class="lni lni-dashboard me-2"></i>Dashboard</a></li>
                <li><a href="dashboard-my-listings.html"><i class="lni lni-files me-2"></i>My Listings</a></li>
                <li><a href="dashboard-add-listings.html"><i class="lni lni-add-files me-2"></i>Add Listing</a></li>
                <li><a href="dashboard-saved-listings.html"><i class="lni lni-bookmark me-2"></i>Saved Listing</a></li>
                <li><a href="dashboard-my-bookings.html"><i class="lni lni-briefcase me-2"></i>My Bookings<span class="count-tag bg-warning">4</span></a></li>
                <li><a href="dashboard-wallet.html"><i class="lni lni-mastercard me-2"></i>Wallet</a></li>
                <li><a href="dashboard-messages.html"><i class="lni lni-envelope me-2"></i>Messages<span class="count-tag bg-sky">4</span></a></li>
                <li><a href="dashboard-my-profile.html"><i class="lni lni-user me-2"></i>My Profile </a></li>
                <li><a href="dashboard-change-password.html"><i class="lni lni-lock-alt me-2"></i>Change Password</a></li>
            </ul>`; // Create the submenus
            dashboardLink.style.display = 'block';

            headerActions.innerHTML = `
                <li>
                    <span class="user-info">
                        Logged in as <strong>${user.name || user.email}</strong>
                    </span>
                </li>
                <li><a href="#" id="logout-btn" class="logout-link"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
            `;

            // Add event listener to the logout button
            document.getElementById('logout-btn').addEventListener('click', (e) => {
                e.preventDefault();
                logout(); // Call the logout function from auth.js
            });

        } else {
            // User is not logged in
            headerActions.innerHTML = `
                <li>
                    <a href="#" data-bs-toggle="modal" data-bs-target="#login" class="ft-bold">
                        <i class="fas fa-sign-in-alt me-1 theme-cl"></i>Sign In
                    </a>
                </li>
                <li class="add-listing">
                    <a href="add-listing.html" >
                        <i class="fas fa-plus me-2"></i>Add Listing
                    </a>
                </li>
            `;
            dashboardLink.style.display = 'none';
        }
    }

    // Check authentication status when the page loads
    checkAuth(updateHeader);

    // Optional: Add event listener for mobile menu toggle (if you have one)
    const navToggle = document.querySelector('.nav-toggle');
    if (navToggle) {
        navToggle.addEventListener('click', () => {
            document.querySelector('.nav-menus-wrapper').classList.toggle('show-menu');
        });
    }
});

// Export the module for use in other files if needed
export default {};
