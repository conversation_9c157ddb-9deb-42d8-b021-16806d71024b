// components/categories.js

/**
 * Sample categories data for different listing types
 * In a production environment, this would likely come from an API
 */
export const categories = {
    place: ['Restaurants', 'Cafes', 'Bars', 'Parks', 'Museums', 'Theaters', 'Hotels', 'Shopping Malls', 'Hospitals'],
    car: ['Car Dealers', 'Auto Repair', 'Car Wash', 'Car Rental'],
    real_estate: ['Apartments', 'Houses', 'Commercial', 'Land'],
    event: ['Concerts', 'Festivals', 'Conferences', 'Workshops', 'Sports'],
    job: ['Full-Time', 'Part-Time', 'Contract', 'Remote', 'Internship'],
    hotel: ['Luxury', 'Budget', 'Resort', 'Boutique', 'Bed & Breakfast']
};
