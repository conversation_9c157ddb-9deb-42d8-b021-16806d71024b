'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import AdminLayout from '@/layouts/AdminLayout';
import { useAuth } from '@/hooks/useAuth';
import { FaArrowLeft, FaSave, FaTimes, FaUpload, FaLink, FaCalendarAlt, FaMoneyBillWave, FaMapMarkerAlt, FaTag } from 'react-icons/fa';

export default function CreateAdvertisement() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  
  const [businesses, setBusinesses] = useState([]);
  const [categories, setCategories] = useState([]);
  const [locations, setLocations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [previewImage, setPreviewImage] = useState(null);
  
  // Form state
  const [formData, setFormData] = useState({
    businessId: '',
    imageUrl: '',
    clickUrl: '',
    startDate: '',
    endDate: '',
    budget: '',
    targetCategories: [],
    targetLocations: [],
    status: 'pending'
  });
  
  // Form validation
  const [formErrors, setFormErrors] = useState({});
  
  useEffect(() => {
    if (authLoading) return;
    
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      
      try {
        // In a real implementation, these would be API calls
        // For now, we'll use mock data
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Mock businesses
        const mockBusinesses = [
          { id: 'biz1', name: 'Cafe Delight', category: 'Restaurant' },
          { id: 'biz2', name: 'Tech Solutions', category: 'IT Services' },
          { id: 'biz3', name: 'Fitness Hub', category: 'Gym' },
          { id: 'biz4', name: 'Beauty Salon', category: 'Beauty' },
          { id: 'biz5', name: 'Auto Repair Shop', category: 'Automotive' },
          { id: 'biz6', name: 'Luxury Hotel', category: 'Accommodation' },
          { id: 'biz7', name: 'Organic Farm', category: 'Agriculture' },
          { id: 'biz8', name: 'Educational Institute', category: 'Education' }
        ];
        
        // Mock categories
        const mockCategories = [
          { id: 'cat1', name: 'Restaurant' },
          { id: 'cat2', name: 'IT Services' },
          { id: 'cat3', name: 'Gym' },
          { id: 'cat4', name: 'Beauty' },
          { id: 'cat5', name: 'Automotive' },
          { id: 'cat6', name: 'Accommodation' },
          { id: 'cat7', name: 'Agriculture' },
          { id: 'cat8', name: 'Education' },
          { id: 'cat9', name: 'Health' },
          { id: 'cat10', name: 'Fashion' },
          { id: 'cat11', name: 'Entertainment' },
          { id: 'cat12', name: 'Real Estate' }
        ];
        
        // Mock locations
        const mockLocations = [
          { id: 'loc1', name: 'Lagos' },
          { id: 'loc2', name: 'Abuja' },
          { id: 'loc3', name: 'Port Harcourt' },
          { id: 'loc4', name: 'Kano' },
          { id: 'loc5', name: 'Ibadan' },
          { id: 'loc6', name: 'Enugu' },
          { id: 'loc7', name: 'Calabar' },
          { id: 'loc8', name: 'Kaduna' },
          { id: 'loc9', name: 'Benin City' },
          { id: 'loc10', name: 'Owerri' }
        ];
        
        setBusinesses(mockBusinesses);
        setCategories(mockCategories);
        setLocations(mockLocations);
        
        // Set default dates
        const today = new Date();
        const oneMonthLater = new Date();
        oneMonthLater.setMonth(today.getMonth() + 1);
        
        setFormData(prevData => ({
          ...prevData,
          startDate: today.toISOString().split('T')[0],
          endDate: oneMonthLater.toISOString().split('T')[0]
        }));
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load required data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [authLoading]);
  
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prevData => ({
      ...prevData,
      [name]: value
    }));
    
    // Clear error for this field
    if (formErrors[name]) {
      setFormErrors(prevErrors => ({
        ...prevErrors,
        [name]: null
      }));
    }
  };
  
  const handleMultiSelectChange = (e) => {
    const { name, options } = e.target;
    const selectedValues = Array.from(options)
      .filter(option => option.selected)
      .map(option => option.value);
    
    setFormData(prevData => ({
      ...prevData,
      [name]: selectedValues
    }));
    
    // Clear error for this field
    if (formErrors[name]) {
      setFormErrors(prevErrors => ({
        ...prevErrors,
        [name]: null
      }));
    }
  };
  
  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (!file) return;
    
    // Check file type
    if (!file.type.startsWith('image/')) {
      setFormErrors(prevErrors => ({
        ...prevErrors,
        imageUrl: 'Please upload an image file (JPEG, PNG, etc.)'
      }));
      return;
    }
    
    // Check file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      setFormErrors(prevErrors => ({
        ...prevErrors,
        imageUrl: 'Image size should be less than 2MB'
      }));
      return;
    }
    
    // Create a preview URL
    const reader = new FileReader();
    reader.onload = () => {
      setPreviewImage(reader.result);
      
      // In a real implementation, you would upload the image to a server
      // and get back a URL. For now, we'll just use the preview URL.
      setFormData(prevData => ({
        ...prevData,
        imageUrl: reader.result
      }));
      
      // Clear error for this field
      if (formErrors.imageUrl) {
        setFormErrors(prevErrors => ({
          ...prevErrors,
          imageUrl: null
        }));
      }
    };
    reader.readAsDataURL(file);
  };
  
  const validateForm = () => {
    const errors = {};
    
    if (!formData.businessId) {
      errors.businessId = 'Please select a business';
    }
    
    if (!formData.imageUrl) {
      errors.imageUrl = 'Please upload an advertisement image';
    }
    
    if (!formData.clickUrl) {
      errors.clickUrl = 'Please enter a destination URL';
    } else if (!isValidUrl(formData.clickUrl)) {
      errors.clickUrl = 'Please enter a valid URL (e.g., https://example.com)';
    }
    
    if (!formData.startDate) {
      errors.startDate = 'Please select a start date';
    }
    
    if (!formData.endDate) {
      errors.endDate = 'Please select an end date';
    } else if (new Date(formData.endDate) <= new Date(formData.startDate)) {
      errors.endDate = 'End date must be after start date';
    }
    
    if (!formData.budget) {
      errors.budget = 'Please enter a budget amount';
    } else if (isNaN(formData.budget) || parseFloat(formData.budget) <= 0) {
      errors.budget = 'Please enter a valid budget amount';
    }
    
    if (formData.targetCategories.length === 0) {
      errors.targetCategories = 'Please select at least one category';
    }
    
    if (formData.targetLocations.length === 0) {
      errors.targetLocations = 'Please select at least one location';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };
  
  const isValidUrl = (url) => {
    try {
      new URL(url);
      return true;
    } catch (err) {
      return false;
    }
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      // Scroll to the first error
      const firstErrorField = Object.keys(formErrors)[0];
      const element = document.querySelector(`[name="${firstErrorField}"]`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      return;
    }
    
    setSubmitting(true);
    
    try {
      // In a real implementation, this would be an API call
      console.log('Creating advertisement with data:', formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Redirect to the advertisements list page
      router.push('/admin/manage-advertisements');
    } catch (err) {
      console.error('Error creating advertisement:', err);
      setError('Failed to create advertisement. Please try again later.');
      window.scrollTo(0, 0);
    } finally {
      setSubmitting(false);
    }
  };
  
  if (authLoading || loading) {
    return (
      <AdminLayout>
        <div className="text-center py-5">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Loading...</p>
        </div>
      </AdminLayout>
    );
  }
  
  return (
    <AdminLayout>
      <div className="container-fluid">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h1 className="h3 mb-0 text-gray-800">Create New Advertisement</h1>
          <Link href="/admin/manage-advertisements" className="btn btn-outline-primary">
            <FaArrowLeft className="me-2" /> Back to Advertisements
          </Link>
        </div>
        
        {error && (
          <div className="alert alert-danger" role="alert">
            {error}
          </div>
        )}
        
        <div className="row">
          <div className="col-lg-8">
            <div className="card shadow mb-4">
              <div className="card-header py-3">
                <h6 className="m-0 font-weight-bold text-primary">Advertisement Details</h6>
              </div>
              <div className="card-body">
                <form onSubmit={handleSubmit}>
                  <div className="row mb-3">
                    <div className="col-md-12">
                      <label htmlFor="businessId" className="form-label">
                        <FaTag className="me-2" /> Business
                      </label>
                      <select
                        id="businessId"
                        name="businessId"
                        className={`form-select ${formErrors.businessId ? 'is-invalid' : ''}`}
                        value={formData.businessId}
                        onChange={handleInputChange}
                      >
                        <option value="">Select a business</option>
                        {businesses.map(business => (
                          <option key={business.id} value={business.id}>
                            {business.name} ({business.category})
                          </option>
                        ))}
                      </select>
                      {formErrors.businessId && (
                        <div className="invalid-feedback">
                          {formErrors.businessId}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="row mb-3">
                    <div className="col-md-12">
                      <label htmlFor="imageUpload" className="form-label">
                        <FaUpload className="me-2" /> Advertisement Image
                      </label>
                      <div className={`input-group ${formErrors.imageUrl ? 'is-invalid' : ''}`}>
                        <input
                          type="file"
                          className="form-control"
                          id="imageUpload"
                          accept="image/*"
                          onChange={handleImageUpload}
                        />
                        <label className="input-group-text" htmlFor="imageUpload">
                          Upload
                        </label>
                      </div>
                      <small className="text-muted">
                        Recommended size: 1200x628 pixels (2:1 ratio). Max file size: 2MB.
                      </small>
                      {formErrors.imageUrl && (
                        <div className="invalid-feedback d-block">
                          {formErrors.imageUrl}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="row mb-3">
                    <div className="col-md-12">
                      <label htmlFor="clickUrl" className="form-label">
                        <FaLink className="me-2" /> Destination URL
                      </label>
                      <input
                        type="url"
                        className={`form-control ${formErrors.clickUrl ? 'is-invalid' : ''}`}
                        id="clickUrl"
                        name="clickUrl"
                        placeholder="https://example.com"
                        value={formData.clickUrl}
                        onChange={handleInputChange}
                      />
                      <small className="text-muted">
                        The URL where users will be directed when they click on the ad.
                      </small>
                      {formErrors.clickUrl && (
                        <div className="invalid-feedback">
                          {formErrors.clickUrl}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="row mb-3">
                    <div className="col-md-6">
                      <label htmlFor="startDate" className="form-label">
                        <FaCalendarAlt className="me-2" /> Start Date
                      </label>
                      <input
                        type="date"
                        className={`form-control ${formErrors.startDate ? 'is-invalid' : ''}`}
                        id="startDate"
                        name="startDate"
                        value={formData.startDate}
                        onChange={handleInputChange}
                      />
                      {formErrors.startDate && (
                        <div className="invalid-feedback">
                          {formErrors.startDate}
                        </div>
                      )}
                    </div>
                    <div className="col-md-6">
                      <label htmlFor="endDate" className="form-label">
                        <FaCalendarAlt className="me-2" /> End Date
                      </label>
                      <input
                        type="date"
                        className={`form-control ${formErrors.endDate ? 'is-invalid' : ''}`}
                        id="endDate"
                        name="endDate"
                        value={formData.endDate}
                        onChange={handleInputChange}
                      />
                      {formErrors.endDate && (
                        <div className="invalid-feedback">
                          {formErrors.endDate}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="row mb-3">
                    <div className="col-md-12">
                      <label htmlFor="budget" className="form-label">
                        <FaMoneyBillWave className="me-2" /> Budget (NGN)
                      </label>
                      <div className="input-group">
                        <span className="input-group-text">₦</span>
                        <input
                          type="number"
                          className={`form-control ${formErrors.budget ? 'is-invalid' : ''}`}
                          id="budget"
                          name="budget"
                          placeholder="10000"
                          min="1000"
                          step="1000"
                          value={formData.budget}
                          onChange={handleInputChange}
                        />
                      </div>
                      <small className="text-muted">
                        Minimum budget: ₦1,000. Higher budgets get more visibility.
                      </small>
                      {formErrors.budget && (
                        <div className="invalid-feedback d-block">
                          {formErrors.budget}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="row mb-3">
                    <div className="col-md-6">
                      <label htmlFor="targetCategories" className="form-label">
                        <FaTag className="me-2" /> Target Categories
                      </label>
                      <select
                        multiple
                        className={`form-select ${formErrors.targetCategories ? 'is-invalid' : ''}`}
                        id="targetCategories"
                        name="targetCategories"
                        size="6"
                        value={formData.targetCategories}
                        onChange={handleMultiSelectChange}
                      >
                        {categories.map(category => (
                          <option key={category.id} value={category.name}>
                            {category.name}
                          </option>
                        ))}
                      </select>
                      <small className="text-muted">
                        Hold Ctrl (or Cmd on Mac) to select multiple categories.
                      </small>
                      {formErrors.targetCategories && (
                        <div className="invalid-feedback d-block">
                          {formErrors.targetCategories}
                        </div>
                      )}
                    </div>
                    <div className="col-md-6">
                      <label htmlFor="targetLocations" className="form-label">
                        <FaMapMarkerAlt className="me-2" /> Target Locations
                      </label>
                      <select
                        multiple
                        className={`form-select ${formErrors.targetLocations ? 'is-invalid' : ''}`}
                        id="targetLocations"
                        name="targetLocations"
                        size="6"
                        value={formData.targetLocations}
                        onChange={handleMultiSelectChange}
                      >
                        {locations.map(location => (
                          <option key={location.id} value={location.name}>
                            {location.name}
                          </option>
                        ))}
                      </select>
                      <small className="text-muted">
                        Hold Ctrl (or Cmd on Mac) to select multiple locations.
                      </small>
                      {formErrors.targetLocations && (
                        <div className="invalid-feedback d-block">
                          {formErrors.targetLocations}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="row mb-3">
                    <div className="col-md-12">
                      <label htmlFor="status" className="form-label">
                        Status
                      </label>
                      <select
                        className="form-select"
                        id="status"
                        name="status"
                        value={formData.status}
                        onChange={handleInputChange}
                      >
                        <option value="pending">Pending Approval</option>
                        <option value="active">Active</option>
                      </select>
                      <small className="text-muted">
                        New advertisements are typically set to "Pending Approval" until reviewed by an admin.
                      </small>
                    </div>
                  </div>
                  
                  <div className="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                    <Link href="/admin/manage-advertisements" className="btn btn-secondary me-md-2">
                      <FaTimes className="me-2" /> Cancel
                    </Link>
                    <button 
                      type="submit" 
                      className="btn btn-primary"
                      disabled={submitting}
                    >
                      {submitting ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                          Creating...
                        </>
                      ) : (
                        <>
                          <FaSave className="me-2" /> Create Advertisement
                        </>
                      )}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
          
          <div className="col-lg-4">
            <div className="card shadow mb-4">
              <div className="card-header py-3">
                <h6 className="m-0 font-weight-bold text-primary">Advertisement Preview</h6>
              </div>
              <div className="card-body">
                {previewImage ? (
                  <div className="ad-preview text-center">
                    <div className="position-relative mb-3">
                      <Image 
                        src={previewImage} 
                        alt="Advertisement Preview" 
                        width={300} 
                        height={158} 
                        className="img-fluid rounded"
                      />
                      <span className="position-absolute top-0 end-0 badge bg-secondary m-2">Ad</span>
                    </div>
                    {formData.businessId && (
                      <div className="small text-muted mb-2">
                        {businesses.find(b => b.id === formData.businessId)?.name || 'Selected Business'}
                      </div>
                    )}
                    {formData.clickUrl && (
                      <div className="small text-truncate">
                        <a href={formData.clickUrl} target="_blank" rel="noopener noreferrer" className="text-decoration-none">
                          {formData.clickUrl}
                        </a>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-5 bg-light rounded">
                    <FaUpload className="display-4 text-muted mb-3" />
                    <p className="text-muted">Upload an image to see the preview</p>
                  </div>
                )}
              </div>
            </div>
            
            <div className="card shadow mb-4">
              <div className="card-header py-3">
                <h6 className="m-0 font-weight-bold text-primary">Advertisement Guidelines</h6>
              </div>
              <div className="card-body">
                <ul className="list-group list-group-flush">
                  <li className="list-group-item">
                    <strong>Image Requirements:</strong>
                    <ul className="small mt-1">
                      <li>Recommended size: 1200x628 pixels (2:1 ratio)</li>
                      <li>Maximum file size: 2MB</li>
                      <li>Formats: JPEG, PNG, GIF</li>
                    </ul>
                  </li>
                  <li className="list-group-item">
                    <strong>Content Guidelines:</strong>
                    <ul className="small mt-1">
                      <li>No misleading or false information</li>
                      <li>No offensive or inappropriate content</li>
                      <li>Text should be clear and legible</li>
                      <li>Avoid excessive text in images</li>
                    </ul>
                  </li>
                  <li className="list-group-item">
                    <strong>Targeting Tips:</strong>
                    <ul className="small mt-1">
                      <li>Select relevant categories for better performance</li>
                      <li>Target specific locations for local businesses</li>
                      <li>Higher budgets increase visibility</li>
                    </ul>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
