'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import AdminLayout from '@/layouts/AdminLayout';
import BlogEditor from '@/components/BlogEditor';
import { useAuth } from '@/hooks/useAuth';

export default function EditBlogPost() {
  const { id } = useParams();
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [post, setPost] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Redirect if user is not logged in or not an admin
  useEffect(() => {
    if (!authLoading && (!user || (user.role !== 'admin' && user.role !== 'super_admin'))) {
      router.push('/login?redirect=/admin/blog/edit/' + id);
    }
  }, [user, authLoading, router, id]);
  
  // Fetch post data
  useEffect(() => {
    const fetchPost = async () => {
      if (!user || !id) return;
      
      setLoading(true);
      setError(null);
      
      try {
        const response = await fetch(`/api/blog/${id}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch blog post');
        }
        
        const data = await response.json();
        setPost(data.post);
        
      } catch (err) {
        console.error('Error fetching blog post:', err);
        setError('Failed to load blog post. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    if (user && (user.role === 'admin' || user.role === 'super_admin')) {
      fetchPost();
    }
  }, [user, id]);
  
  if (authLoading || loading) {
    return (
      <AdminLayout>
        <div className="text-center py-5">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Loading blog post...</p>
        </div>
      </AdminLayout>
    );
  }
  
  if (error) {
    return (
      <AdminLayout>
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
        <div className="text-center mt-4">
          <Link href="/admin/blog" className="btn btn-primary">
            Back to Blog
          </Link>
        </div>
      </AdminLayout>
    );
  }
  
  if (!post) {
    return (
      <AdminLayout>
        <div className="text-center py-5">
          <h2>Post Not Found</h2>
          <p>The blog post you're looking for doesn't exist or has been removed.</p>
          <Link href="/admin/blog" className="btn btn-primary mt-3">
            Back to Blog
          </Link>
        </div>
      </AdminLayout>
    );
  }
  
  // If user is not logged in or not an admin, show nothing (will redirect)
  if (!user || (user.role !== 'admin' && user.role !== 'super_admin')) {
    return null;
  }
  
  return (
    <AdminLayout>
      <div className="admin-blog-edit">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h1 className="h3 mb-0 text-gray-800">Edit Blog Post</h1>
          <Link href="/admin/blog" className="btn btn-secondary">
            <i className="fas fa-arrow-left me-2"></i>Back to Blog
          </Link>
        </div>
        
        <div className="card shadow mb-4">
          <div className="card-body">
            <BlogEditor initialData={post} isEditing={true} />
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
