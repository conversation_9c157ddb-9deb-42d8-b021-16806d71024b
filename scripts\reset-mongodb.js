/**
 * MongoDB Reset Script
 * 
 * This script resets the MongoDB database by dropping all collections.
 * Use with caution as it will delete all data in the database.
 * 
 * Usage:
 * node scripts/reset-mongodb.js
 */

require('dotenv').config();
const { MongoClient } = require('mongodb');
const readline = require('readline');

// MongoDB configuration
const mongoUri = process.env.MONGODB_URI;
const dbName = process.env.MONGODB_DB_NAME || 'findapro';

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Connect to MongoDB
async function connectToMongoDB() {
  const client = new MongoClient(mongoUri);
  await client.connect();
  console.log('Connected to MongoDB');
  return { client, db: client.db(dbName) };
}

// Reset MongoDB database
async function resetMongoDB() {
  let client;
  
  try {
    // Connect to MongoDB
    const { client: mongoClient, db } = await connectToMongoDB();
    client = mongoClient;
    
    // Get all collections
    const collections = await db.listCollections().toArray();
    
    if (collections.length === 0) {
      console.log('No collections found in the database.');
      return;
    }
    
    console.log(`Found ${collections.length} collections in the database:`);
    collections.forEach((collection, index) => {
      console.log(`${index + 1}. ${collection.name}`);
    });
    
    // Ask for confirmation
    rl.question('\nWARNING: This will delete all data in the database. Are you sure you want to continue? (yes/no): ', async (answer) => {
      if (answer.toLowerCase() === 'yes') {
        console.log('\nResetting MongoDB database...');
        
        // Drop each collection
        for (const collection of collections) {
          await db.collection(collection.name).drop();
          console.log(`Dropped collection: ${collection.name}`);
        }
        
        console.log('\nMongoDB database has been reset successfully.');
      } else {
        console.log('\nOperation cancelled. No changes were made to the database.');
      }
      
      // Close MongoDB connection and readline interface
      if (client) {
        await client.close();
        console.log('MongoDB connection closed');
      }
      
      rl.close();
    });
  } catch (error) {
    console.error('Error during reset:', error);
    
    // Close MongoDB connection and readline interface
    if (client) {
      await client.close();
      console.log('MongoDB connection closed');
    }
    
    rl.close();
  }
}

// Run the reset
resetMongoDB();
