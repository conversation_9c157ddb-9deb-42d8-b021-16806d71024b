'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import AdminLayout from '@/layouts/AdminLayout';
import { useAuth } from '@/hooks/useAuth';

export default function AdminContentPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [activeTab, setActiveTab] = useState('pages');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pages, setPages] = useState([]);
  const [blogPosts, setBlogPosts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [tags, setTags] = useState([]);
  
  // Redirect if user is not logged in or not an admin
  useEffect(() => {
    if (!authLoading && (!user || (user.role !== 'admin' && user.role !== 'super_admin'))) {
      router.push('/login?redirect=/admin/content');
    }
  }, [user, authLoading, router]);
  
  // Fetch content data based on active tab
  useEffect(() => {
    const fetchContent = async () => {
      if (!user) return;
      
      setLoading(true);
      setError(null);
      
      try {
        // For now, we'll use mock data
        // In a real implementation, you would fetch this data from your API
        
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800));
        
        if (activeTab === 'pages') {
          // Mock pages data
          setPages([
            { id: 'p1', title: 'Home Page', slug: 'home', lastUpdated: '2023-06-15T10:30:00Z', status: 'published' },
            { id: 'p2', title: 'About Us', slug: 'about', lastUpdated: '2023-06-14T14:45:00Z', status: 'published' },
            { id: 'p3', title: 'Contact Us', slug: 'contact', lastUpdated: '2023-06-13T09:15:00Z', status: 'published' },
            { id: 'p4', title: 'Privacy Policy', slug: 'privacy-policy', lastUpdated: '2023-06-12T16:20:00Z', status: 'published' },
            { id: 'p5', title: 'Terms of Service', slug: 'terms-of-service', lastUpdated: '2023-06-11T11:10:00Z', status: 'published' },
            { id: 'p6', title: 'FAQ', slug: 'faq', lastUpdated: '2023-06-10T13:25:00Z', status: 'published' },
            { id: 'p7', title: 'How It Works', slug: 'how-it-works', lastUpdated: '2023-06-09T15:40:00Z', status: 'draft' }
          ]);
        } else if (activeTab === 'blog') {
          // Mock blog posts data
          setBlogPosts([
            { id: 'b1', title: 'Getting Started with Finda', slug: 'getting-started', category: 'Guide', author: 'Admin', lastUpdated: '2023-06-15T08:30:00Z', status: 'published' },
            { id: 'b2', title: 'Top 10 Businesses in Lagos', slug: 'top-businesses-lagos', category: 'Business', author: 'Admin', lastUpdated: '2023-06-14T13:45:00Z', status: 'published' },
            { id: 'b3', title: 'How to Verify Your Business', slug: 'verify-business', category: 'Guide', author: 'Admin', lastUpdated: '2023-06-13T10:15:00Z', status: 'published' },
            { id: 'b4', title: 'Upcoming Features in Finda', slug: 'upcoming-features', category: 'News', author: 'Admin', lastUpdated: '2023-06-12T15:20:00Z', status: 'draft' },
            { id: 'b5', title: 'Business Spotlight: Tech Solutions', slug: 'business-spotlight-tech', category: 'Spotlight', author: 'Admin', lastUpdated: '2023-06-11T09:10:00Z', status: 'published' }
          ]);
        } else if (activeTab === 'categories') {
          // Mock categories data
          setCategories([
            { id: 'c1', name: 'Guide', slug: 'guide', postCount: 2, lastUpdated: '2023-06-15T10:30:00Z' },
            { id: 'c2', name: 'Business', slug: 'business', postCount: 1, lastUpdated: '2023-06-14T14:45:00Z' },
            { id: 'c3', name: 'News', slug: 'news', postCount: 1, lastUpdated: '2023-06-13T09:15:00Z' },
            { id: 'c4', name: 'Spotlight', slug: 'spotlight', postCount: 1, lastUpdated: '2023-06-12T16:20:00Z' },
            { id: 'c5', name: 'Tips', slug: 'tips', postCount: 0, lastUpdated: '2023-06-11T11:10:00Z' }
          ]);
        } else if (activeTab === 'tags') {
          // Mock tags data
          setTags([
            { id: 't1', name: 'Business', slug: 'business', postCount: 3, lastUpdated: '2023-06-15T10:30:00Z' },
            { id: 't2', name: 'Guide', slug: 'guide', postCount: 2, lastUpdated: '2023-06-14T14:45:00Z' },
            { id: 't3', name: 'Tips', slug: 'tips', postCount: 2, lastUpdated: '2023-06-13T09:15:00Z' },
            { id: 't4', name: 'Lagos', slug: 'lagos', postCount: 1, lastUpdated: '2023-06-12T16:20:00Z' },
            { id: 't5', name: 'Verification', slug: 'verification', postCount: 1, lastUpdated: '2023-06-11T11:10:00Z' },
            { id: 't6', name: 'Features', slug: 'features', postCount: 1, lastUpdated: '2023-06-10T13:25:00Z' },
            { id: 't7', name: 'Spotlight', slug: 'spotlight', postCount: 1, lastUpdated: '2023-06-09T15:40:00Z' }
          ]);
        }
        
      } catch (err) {
        console.error('Error fetching content:', err);
        setError('Failed to load content data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    if (user && (user.role === 'admin' || user.role === 'super_admin')) {
      fetchContent();
    }
  }, [user, activeTab]);
  
  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };
  
  // Handle delete page
  const handleDeletePage = (id) => {
    if (!window.confirm('Are you sure you want to delete this page? This action cannot be undone.')) {
      return;
    }
    
    // In a real implementation, you would call your API to delete the page
    setPages(pages.filter(page => page.id !== id));
  };
  
  // Handle delete blog post
  const handleDeleteBlogPost = (id) => {
    if (!window.confirm('Are you sure you want to delete this blog post? This action cannot be undone.')) {
      return;
    }
    
    // In a real implementation, you would call your API to delete the blog post
    setBlogPosts(blogPosts.filter(post => post.id !== id));
  };
  
  // Handle delete category
  const handleDeleteCategory = (id) => {
    if (!window.confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
      return;
    }
    
    // In a real implementation, you would call your API to delete the category
    setCategories(categories.filter(category => category.id !== id));
  };
  
  // Handle delete tag
  const handleDeleteTag = (id) => {
    if (!window.confirm('Are you sure you want to delete this tag? This action cannot be undone.')) {
      return;
    }
    
    // In a real implementation, you would call your API to delete the tag
    setTags(tags.filter(tag => tag.id !== id));
  };
  
  if (authLoading || loading) {
    return (
      <AdminLayout>
        <div className="text-center py-5">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Loading content data...</p>
        </div>
      </AdminLayout>
    );
  }
  
  if (error) {
    return (
      <AdminLayout>
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      </AdminLayout>
    );
  }
  
  // If user is not logged in or not an admin, show nothing (will redirect)
  if (!user || (user.role !== 'admin' && user.role !== 'super_admin')) {
    return null;
  }
  
  return (
    <AdminLayout>
      <div className="admin-content">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h1 className="h3 mb-0 text-gray-800">Content Management</h1>
          <div className="btn-group">
            {activeTab === 'pages' && (
              <Link href="/admin/content/pages/new" className="btn btn-primary">
                <i className="fas fa-plus me-2"></i>Add New Page
              </Link>
            )}
            {activeTab === 'blog' && (
              <Link href="/admin/blog/new" className="btn btn-primary">
                <i className="fas fa-plus me-2"></i>Add New Post
              </Link>
            )}
            {activeTab === 'categories' && (
              <Link href="/admin/blog/categories" className="btn btn-primary">
                <i className="fas fa-plus me-2"></i>Add New Category
              </Link>
            )}
            {activeTab === 'tags' && (
              <Link href="/admin/blog/tags" className="btn btn-primary">
                <i className="fas fa-plus me-2"></i>Add New Tag
              </Link>
            )}
          </div>
        </div>
        
        {/* Content Tabs */}
        <ul className="nav nav-tabs mb-4">
          <li className="nav-item">
            <button 
              className={`nav-link ${activeTab === 'pages' ? 'active' : ''}`}
              onClick={() => setActiveTab('pages')}
            >
              Pages
            </button>
          </li>
          <li className="nav-item">
            <button 
              className={`nav-link ${activeTab === 'blog' ? 'active' : ''}`}
              onClick={() => setActiveTab('blog')}
            >
              Blog Posts
            </button>
          </li>
          <li className="nav-item">
            <button 
              className={`nav-link ${activeTab === 'categories' ? 'active' : ''}`}
              onClick={() => setActiveTab('categories')}
            >
              Categories
            </button>
          </li>
          <li className="nav-item">
            <button 
              className={`nav-link ${activeTab === 'tags' ? 'active' : ''}`}
              onClick={() => setActiveTab('tags')}
            >
              Tags
            </button>
          </li>
        </ul>
        
        {/* Pages Tab Content */}
        {activeTab === 'pages' && (
          <div className="card shadow mb-4">
            <div className="card-body">
              <div className="table-responsive">
                <table className="table table-bordered" width="100%" cellSpacing="0">
                  <thead>
                    <tr>
                      <th>Title</th>
                      <th>Slug</th>
                      <th>Status</th>
                      <th>Last Updated</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {pages.map((page) => (
                      <tr key={page.id}>
                        <td>{page.title}</td>
                        <td>{page.slug}</td>
                        <td>
                          <span className={`badge ${page.status === 'published' ? 'bg-success' : 'bg-warning text-dark'}`}>
                            {page.status === 'published' ? 'Published' : 'Draft'}
                          </span>
                        </td>
                        <td>{formatDate(page.lastUpdated)}</td>
                        <td>
                          <div className="btn-group">
                            <Link 
                              href={`/${page.slug}`} 
                              className="btn btn-sm btn-info"
                              target="_blank"
                            >
                              <i className="fas fa-eye"></i>
                            </Link>
                            <Link 
                              href={`/admin/content/pages/edit/${page.id}`} 
                              className="btn btn-sm btn-primary"
                            >
                              <i className="fas fa-edit"></i>
                            </Link>
                            <button 
                              className="btn btn-sm btn-danger"
                              onClick={() => handleDeletePage(page.id)}
                            >
                              <i className="fas fa-trash"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
        
        {/* Blog Posts Tab Content */}
        {activeTab === 'blog' && (
          <div className="card shadow mb-4">
            <div className="card-body">
              <div className="table-responsive">
                <table className="table table-bordered" width="100%" cellSpacing="0">
                  <thead>
                    <tr>
                      <th>Title</th>
                      <th>Category</th>
                      <th>Author</th>
                      <th>Status</th>
                      <th>Last Updated</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {blogPosts.map((post) => (
                      <tr key={post.id}>
                        <td>{post.title}</td>
                        <td>{post.category}</td>
                        <td>{post.author}</td>
                        <td>
                          <span className={`badge ${post.status === 'published' ? 'bg-success' : 'bg-warning text-dark'}`}>
                            {post.status === 'published' ? 'Published' : 'Draft'}
                          </span>
                        </td>
                        <td>{formatDate(post.lastUpdated)}</td>
                        <td>
                          <div className="btn-group">
                            <Link 
                              href={`/blog/${post.slug}`} 
                              className="btn btn-sm btn-info"
                              target="_blank"
                            >
                              <i className="fas fa-eye"></i>
                            </Link>
                            <Link 
                              href={`/admin/blog/edit/${post.id}`} 
                              className="btn btn-sm btn-primary"
                            >
                              <i className="fas fa-edit"></i>
                            </Link>
                            <button 
                              className="btn btn-sm btn-danger"
                              onClick={() => handleDeleteBlogPost(post.id)}
                            >
                              <i className="fas fa-trash"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
        
        {/* Categories Tab Content */}
        {activeTab === 'categories' && (
          <div className="card shadow mb-4">
            <div className="card-body">
              <div className="table-responsive">
                <table className="table table-bordered" width="100%" cellSpacing="0">
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Slug</th>
                      <th>Post Count</th>
                      <th>Last Updated</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {categories.map((category) => (
                      <tr key={category.id}>
                        <td>{category.name}</td>
                        <td>{category.slug}</td>
                        <td>{category.postCount}</td>
                        <td>{formatDate(category.lastUpdated)}</td>
                        <td>
                          <div className="btn-group">
                            <Link 
                              href={`/blog/category/${category.slug}`} 
                              className="btn btn-sm btn-info"
                              target="_blank"
                            >
                              <i className="fas fa-eye"></i>
                            </Link>
                            <Link 
                              href={`/admin/blog/categories?edit=${category.id}`} 
                              className="btn btn-sm btn-primary"
                            >
                              <i className="fas fa-edit"></i>
                            </Link>
                            <button 
                              className="btn btn-sm btn-danger"
                              onClick={() => handleDeleteCategory(category.id)}
                              disabled={category.postCount > 0}
                              title={category.postCount > 0 ? 'Cannot delete category with posts' : 'Delete category'}
                            >
                              <i className="fas fa-trash"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
        
        {/* Tags Tab Content */}
        {activeTab === 'tags' && (
          <div className="card shadow mb-4">
            <div className="card-body">
              <div className="table-responsive">
                <table className="table table-bordered" width="100%" cellSpacing="0">
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Slug</th>
                      <th>Post Count</th>
                      <th>Last Updated</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {tags.map((tag) => (
                      <tr key={tag.id}>
                        <td>{tag.name}</td>
                        <td>{tag.slug}</td>
                        <td>{tag.postCount}</td>
                        <td>{formatDate(tag.lastUpdated)}</td>
                        <td>
                          <div className="btn-group">
                            <Link 
                              href={`/blog/tag/${tag.slug}`} 
                              className="btn btn-sm btn-info"
                              target="_blank"
                            >
                              <i className="fas fa-eye"></i>
                            </Link>
                            <Link 
                              href={`/admin/blog/tags?edit=${tag.id}`} 
                              className="btn btn-sm btn-primary"
                            >
                              <i className="fas fa-edit"></i>
                            </Link>
                            <button 
                              className="btn btn-sm btn-danger"
                              onClick={() => handleDeleteTag(tag.id)}
                              disabled={tag.postCount > 0}
                              title={tag.postCount > 0 ? 'Cannot delete tag with posts' : 'Delete tag'}
                            >
                              <i className="fas fa-trash"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
