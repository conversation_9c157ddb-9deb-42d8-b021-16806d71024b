'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import Link from 'next/link';
import Image from 'next/image';
import AdminLayout from '@/layouts/AdminLayout';
import { FaPlus, FaEdit, FaTrash, FaEye, FaCheck, FaTimes, FaFilter, FaSearch, FaSync } from 'react-icons/fa';

export default function ManageAdvertisements() {
  const { user, loading: authLoading } = useAuth();
  const [advertisements, setAdvertisements] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalAds, setTotalAds] = useState(0);
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedAd, setSelectedAd] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);
  const adsPerPage = 10;

  useEffect(() => {
    if (authLoading) return;
    
    fetchAdvertisements();
  }, [authLoading, currentPage, statusFilter, searchTerm]);

  const fetchAdvertisements = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // In a real implementation, this would be an API call with filters
      // For now, we'll use mock data and filter it client-side
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const mockAds = [
        {
          id: 'ad1',
          businessId: {
            id: 'biz1',
            name: 'Cafe Delight',
            category: 'Restaurant'
          },
          imageUrl: '/assets/img/ads/ad1.jpg',
          clickUrl: 'https://example.com/cafe-delight',
          startDate: '2023-07-01',
          endDate: '2023-08-01',
          targetingCriteria: {
            categories: ['restaurant', 'food'],
            locations: ['Lagos']
          },
          budget: 50000,
          clicks: 245,
          impressions: 5678,
          status: 'active',
          createdAt: '2023-06-15T10:30:00Z'
        },
        {
          id: 'ad2',
          businessId: {
            id: 'biz2',
            name: 'Tech Solutions',
            category: 'IT Services'
          },
          imageUrl: '/assets/img/ads/ad2.jpg',
          clickUrl: 'https://example.com/tech-solutions',
          startDate: '2023-07-10',
          endDate: '2023-08-10',
          targetingCriteria: {
            categories: ['technology', 'services'],
            locations: ['Abuja']
          },
          budget: 75000,
          clicks: 189,
          impressions: 4321,
          status: 'pending',
          createdAt: '2023-06-20T14:45:00Z'
        },
        {
          id: 'ad3',
          businessId: {
            id: 'biz3',
            name: 'Fitness Hub',
            category: 'Gym'
          },
          imageUrl: '/assets/img/ads/ad3.jpg',
          clickUrl: 'https://example.com/fitness-hub',
          startDate: '2023-06-15',
          endDate: '2023-07-15',
          targetingCriteria: {
            categories: ['fitness', 'health'],
            locations: ['Port Harcourt']
          },
          budget: 30000,
          clicks: 98,
          impressions: 2345,
          status: 'expired',
          createdAt: '2023-06-01T09:15:00Z'
        },
        {
          id: 'ad4',
          businessId: {
            id: 'biz4',
            name: 'Beauty Salon',
            category: 'Beauty'
          },
          imageUrl: '/assets/img/ads/ad4.jpg',
          clickUrl: 'https://example.com/beauty-salon',
          startDate: '2023-07-05',
          endDate: '2023-08-05',
          targetingCriteria: {
            categories: ['beauty', 'salon'],
            locations: ['Lagos']
          },
          budget: 45000,
          clicks: 156,
          impressions: 3789,
          status: 'active',
          createdAt: '2023-06-25T11:20:00Z'
        },
        {
          id: 'ad5',
          businessId: {
            id: 'biz5',
            name: 'Auto Repair Shop',
            category: 'Automotive'
          },
          imageUrl: '/assets/img/ads/ad5.jpg',
          clickUrl: 'https://example.com/auto-repair',
          startDate: '2023-07-15',
          endDate: '2023-08-15',
          targetingCriteria: {
            categories: ['automotive', 'repair'],
            locations: ['Kano']
          },
          budget: 35000,
          clicks: 0,
          impressions: 0,
          status: 'pending',
          createdAt: '2023-07-01T13:10:00Z'
        },
        {
          id: 'ad6',
          businessId: {
            id: 'biz6',
            name: 'Luxury Hotel',
            category: 'Accommodation'
          },
          imageUrl: '/assets/img/ads/ad6.jpg',
          clickUrl: 'https://example.com/luxury-hotel',
          startDate: '2023-06-10',
          endDate: '2023-07-10',
          targetingCriteria: {
            categories: ['hotel', 'accommodation'],
            locations: ['Lagos', 'Abuja']
          },
          budget: 100000,
          clicks: 312,
          impressions: 7890,
          status: 'expired',
          createdAt: '2023-05-25T15:30:00Z'
        },
        {
          id: 'ad7',
          businessId: {
            id: 'biz7',
            name: 'Organic Farm',
            category: 'Agriculture'
          },
          imageUrl: '/assets/img/ads/ad7.jpg',
          clickUrl: 'https://example.com/organic-farm',
          startDate: '2023-07-20',
          endDate: '2023-08-20',
          targetingCriteria: {
            categories: ['agriculture', 'food'],
            locations: ['Ibadan']
          },
          budget: 25000,
          clicks: 0,
          impressions: 0,
          status: 'pending',
          createdAt: '2023-07-05T09:45:00Z'
        },
        {
          id: 'ad8',
          businessId: {
            id: 'biz8',
            name: 'Educational Institute',
            category: 'Education'
          },
          imageUrl: '/assets/img/ads/ad8.jpg',
          clickUrl: 'https://example.com/educational-institute',
          startDate: '2023-07-01',
          endDate: '2023-08-01',
          targetingCriteria: {
            categories: ['education', 'school'],
            locations: ['Lagos', 'Abuja', 'Port Harcourt']
          },
          budget: 60000,
          clicks: 178,
          impressions: 4567,
          status: 'active',
          createdAt: '2023-06-20T10:15:00Z'
        }
      ];
      
      // Apply filters
      let filteredAds = [...mockAds];
      
      // Status filter
      if (statusFilter !== 'all') {
        filteredAds = filteredAds.filter(ad => ad.status === statusFilter);
      }
      
      // Search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        filteredAds = filteredAds.filter(ad => 
          ad.businessId.name.toLowerCase().includes(searchLower) ||
          ad.businessId.category.toLowerCase().includes(searchLower) ||
          ad.targetingCriteria.categories.some(cat => cat.toLowerCase().includes(searchLower)) ||
          ad.targetingCriteria.locations.some(loc => loc.toLowerCase().includes(searchLower))
        );
      }
      
      setAdvertisements(filteredAds);
      setTotalAds(filteredAds.length);
      setTotalPages(Math.ceil(filteredAds.length / adsPerPage));
    } catch (err) {
      console.error('Error fetching advertisements:', err);
      setError('Failed to load advertisements. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleStatus = async (adId, newStatus) => {
    setActionLoading(true);
    
    try {
      // In a real implementation, this would be an API call
      console.log(`Toggling ad ${adId} status to ${newStatus}`);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Update the local state
      const updatedAds = advertisements.map(ad => {
        if (ad.id === adId) {
          return {
            ...ad,
            status: newStatus
          };
        }
        return ad;
      });
      
      setAdvertisements(updatedAds);
    } catch (err) {
      console.error('Error toggling ad status:', err);
      alert('Failed to update advertisement status. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };

  const openDeleteModal = (ad) => {
    setSelectedAd(ad);
    setShowDeleteModal(true);
  };

  const handleDeleteAd = async () => {
    setActionLoading(true);
    
    try {
      // In a real implementation, this would be an API call
      console.log(`Deleting ad ${selectedAd.id}`);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Update the local state
      const updatedAds = advertisements.filter(ad => ad.id !== selectedAd.id);
      setAdvertisements(updatedAds);
      setTotalAds(updatedAds.length);
      setTotalPages(Math.ceil(updatedAds.length / adsPerPage));
      
      // Close the modal
      setShowDeleteModal(false);
      setSelectedAd(null);
    } catch (err) {
      console.error('Error deleting advertisement:', err);
      alert('Failed to delete advertisement. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'active':
        return 'bg-success';
      case 'pending':
        return 'bg-warning text-dark';
      case 'expired':
        return 'bg-danger';
      case 'rejected':
        return 'bg-danger';
      default:
        return 'bg-secondary';
    }
  };

  // Get current advertisements for pagination
  const indexOfLastAd = currentPage * adsPerPage;
  const indexOfFirstAd = indexOfLastAd - adsPerPage;
  const currentAds = advertisements.slice(indexOfFirstAd, indexOfLastAd);

  // Calculate CTR (Click-Through Rate)
  const calculateCTR = (clicks, impressions) => {
    if (impressions === 0) return '0.00%';
    const ctr = (clicks / impressions) * 100;
    return ctr.toFixed(2) + '%';
  };

  if (authLoading || loading) {
    return (
      <AdminLayout>
        <div className="text-center py-5">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Loading advertisements...</p>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="container-fluid">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h1 className="h3 mb-0 text-gray-800">Manage Advertisements</h1>
          <div>
            <button 
              className="btn btn-outline-primary me-2" 
              onClick={() => setIsFilterOpen(!isFilterOpen)}
            >
              <FaFilter className="me-2" /> {isFilterOpen ? 'Hide Filters' : 'Show Filters'}
            </button>
            <Link href="/admin/manage-advertisements/create" className="btn btn-primary">
              <FaPlus className="me-2" /> Create New Ad
            </Link>
          </div>
        </div>

        {/* Search and Filters */}
        {isFilterOpen && (
          <div className="card shadow mb-4">
            <div className="card-header py-3">
              <h6 className="m-0 font-weight-bold text-primary">Search & Filters</h6>
            </div>
            <div className="card-body">
              <div className="row mb-3">
                <div className="col-md-6">
                  <div className="input-group">
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Search by business, category, or location..."
                      value={searchTerm}
                      onChange={(e) => {
                        setSearchTerm(e.target.value);
                        setCurrentPage(1); // Reset to first page on search
                      }}
                    />
                    <button className="btn btn-primary" type="button">
                      <FaSearch />
                    </button>
                  </div>
                </div>
                <div className="col-md-4">
                  <select 
                    className="form-select" 
                    value={statusFilter}
                    onChange={(e) => {
                      setStatusFilter(e.target.value);
                      setCurrentPage(1); // Reset to first page on filter change
                    }}
                  >
                    <option value="all">All Statuses</option>
                    <option value="active">Active</option>
                    <option value="pending">Pending</option>
                    <option value="expired">Expired</option>
                    <option value="rejected">Rejected</option>
                  </select>
                </div>
                <div className="col-md-2">
                  <button 
                    className="btn btn-outline-secondary w-100" 
                    onClick={() => {
                      setStatusFilter('all');
                      setSearchTerm('');
                      setCurrentPage(1);
                    }}
                  >
                    <FaSync className="me-2" /> Reset
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Stats Cards */}
        <div className="row mb-4">
          <div className="col-xl-3 col-md-6 mb-4">
            <div className="card border-left-primary shadow h-100 py-2">
              <div className="card-body">
                <div className="row no-gutters align-items-center">
                  <div className="col mr-2">
                    <div className="text-xs font-weight-bold text-primary text-uppercase mb-1">
                      Total Advertisements
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">
                      {totalAds}
                    </div>
                  </div>
                  <div className="col-auto">
                    <i className="fas fa-ad fa-2x text-gray-300"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="col-xl-3 col-md-6 mb-4">
            <div className="card border-left-success shadow h-100 py-2">
              <div className="card-body">
                <div className="row no-gutters align-items-center">
                  <div className="col mr-2">
                    <div className="text-xs font-weight-bold text-success text-uppercase mb-1">
                      Active Ads
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">
                      {advertisements.filter(ad => ad.status === 'active').length}
                    </div>
                  </div>
                  <div className="col-auto">
                    <i className="fas fa-check-circle fa-2x text-gray-300"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="col-xl-3 col-md-6 mb-4">
            <div className="card border-left-warning shadow h-100 py-2">
              <div className="card-body">
                <div className="row no-gutters align-items-center">
                  <div className="col mr-2">
                    <div className="text-xs font-weight-bold text-warning text-uppercase mb-1">
                      Pending Approval
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">
                      {advertisements.filter(ad => ad.status === 'pending').length}
                    </div>
                  </div>
                  <div className="col-auto">
                    <i className="fas fa-clock fa-2x text-gray-300"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="col-xl-3 col-md-6 mb-4">
            <div className="card border-left-danger shadow h-100 py-2">
              <div className="card-body">
                <div className="row no-gutters align-items-center">
                  <div className="col mr-2">
                    <div className="text-xs font-weight-bold text-danger text-uppercase mb-1">
                      Expired Ads
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">
                      {advertisements.filter(ad => ad.status === 'expired').length}
                    </div>
                  </div>
                  <div className="col-auto">
                    <i className="fas fa-calendar-times fa-2x text-gray-300"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Advertisements Table */}
        <div className="card shadow mb-4">
          <div className="card-header py-3">
            <h6 className="m-0 font-weight-bold text-primary">All Advertisements</h6>
          </div>
          <div className="card-body">
            {currentAds.length === 0 ? (
              <div className="text-center py-4">
                <p className="mb-0">No advertisements found.</p>
                <Link href="/admin/manage-advertisements/create" className="btn btn-primary mt-3">
                  <FaPlus className="me-2" /> Create New Ad
                </Link>
              </div>
            ) : (
              <div className="table-responsive">
                <table className="table table-bordered" width="100%" cellSpacing="0">
                  <thead>
                    <tr>
                      <th>Business</th>
                      <th>Ad Preview</th>
                      <th>Duration</th>
                      <th>Budget</th>
                      <th>Performance</th>
                      <th>Status</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {currentAds.map((ad) => (
                      <tr key={ad.id}>
                        <td>
                          <Link href={`/admin/manage-businesses/${ad.businessId.id}`} className="text-decoration-none">
                            {ad.businessId.name}
                          </Link>
                          <div className="small text-muted">{ad.businessId.category}</div>
                          <div className="small text-muted">
                            <strong>Targeting:</strong> {ad.targetingCriteria.categories.join(', ')}
                          </div>
                          <div className="small text-muted">
                            <strong>Locations:</strong> {ad.targetingCriteria.locations.join(', ')}
                          </div>
                        </td>
                        <td>
                          <div className="ad-preview">
                            <div className="ad-image mb-2">
                              <Image 
                                src={ad.imageUrl || '/assets/img/placeholder-ad.jpg'} 
                                alt="Advertisement" 
                                width={120} 
                                height={80} 
                                className="img-fluid rounded"
                              />
                            </div>
                            <div className="small text-truncate" style={{ maxWidth: '150px' }}>
                              <a href={ad.clickUrl} target="_blank" rel="noopener noreferrer" className="text-decoration-none">
                                {ad.clickUrl}
                              </a>
                            </div>
                          </div>
                        </td>
                        <td>
                          <div className="small">
                            <strong>Start:</strong> {formatDate(ad.startDate)}
                          </div>
                          <div className="small">
                            <strong>End:</strong> {formatDate(ad.endDate)}
                          </div>
                          <div className="small text-muted">
                            <strong>Created:</strong> {formatDate(ad.createdAt)}
                          </div>
                        </td>
                        <td>
                          <div className="font-weight-bold">
                            {formatCurrency(ad.budget)}
                          </div>
                        </td>
                        <td>
                          <div className="small">
                            <strong>Impressions:</strong> {ad.impressions.toLocaleString()}
                          </div>
                          <div className="small">
                            <strong>Clicks:</strong> {ad.clicks.toLocaleString()}
                          </div>
                          <div className="small">
                            <strong>CTR:</strong> {calculateCTR(ad.clicks, ad.impressions)}
                          </div>
                        </td>
                        <td>
                          <span className={`badge ${getStatusBadgeClass(ad.status)}`}>
                            {ad.status.charAt(0).toUpperCase() + ad.status.slice(1)}
                          </span>
                        </td>
                        <td>
                          <div className="btn-group">
                            <Link href={`/admin/manage-advertisements/${ad.id}`} className="btn btn-sm btn-info me-1">
                              <FaEye /> View
                            </Link>
                            <Link href={`/admin/manage-advertisements/${ad.id}/edit`} className="btn btn-sm btn-primary me-1">
                              <FaEdit /> Edit
                            </Link>
                            {ad.status === 'pending' && (
                              <button
                                className="btn btn-sm btn-success me-1"
                                onClick={() => handleToggleStatus(ad.id, 'active')}
                                disabled={actionLoading}
                              >
                                <FaCheck /> Approve
                              </button>
                            )}
                            {ad.status === 'pending' && (
                              <button
                                className="btn btn-sm btn-danger me-1"
                                onClick={() => handleToggleStatus(ad.id, 'rejected')}
                                disabled={actionLoading}
                              >
                                <FaTimes /> Reject
                              </button>
                            )}
                            {ad.status === 'active' && (
                              <button
                                className="btn btn-sm btn-warning me-1"
                                onClick={() => handleToggleStatus(ad.id, 'expired')}
                                disabled={actionLoading}
                              >
                                <FaTimes /> Deactivate
                              </button>
                            )}
                            <button
                              className="btn btn-sm btn-danger"
                              onClick={() => openDeleteModal(ad)}
                              disabled={actionLoading}
                            >
                              <FaTrash /> Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <nav className="mt-4">
                <ul className="pagination justify-content-center">
                  <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                    <button
                      className="page-link"
                      onClick={() => setCurrentPage(currentPage - 1)}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </button>
                  </li>
                  {[...Array(totalPages)].map((_, index) => (
                    <li
                      key={index}
                      className={`page-item ${currentPage === index + 1 ? 'active' : ''}`}
                    >
                      <button
                        className="page-link"
                        onClick={() => setCurrentPage(index + 1)}
                      >
                        {index + 1}
                      </button>
                    </li>
                  ))}
                  <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>
                    <button
                      className="page-link"
                      onClick={() => setCurrentPage(currentPage + 1)}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </button>
                  </li>
                </ul>
              </nav>
            )}
          </div>
        </div>
      </div>

      {/* Delete Advertisement Modal */}
      {showDeleteModal && (
        <div className="modal fade show" style={{ display: 'block' }} tabIndex="-1">
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Delete Advertisement</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setShowDeleteModal(false);
                    setSelectedAd(null);
                  }}
                ></button>
              </div>
              <div className="modal-body">
                <p>Are you sure you want to delete this advertisement?</p>
                <div className="alert alert-warning">
                  <i className="fas fa-exclamation-triangle me-2"></i>
                  This action cannot be undone. The advertisement will be permanently removed from the system.
                </div>
                {selectedAd && (
                  <div className="ad-details mt-3">
                    <p><strong>Business:</strong> {selectedAd.businessId.name}</p>
                    <p><strong>Status:</strong> {selectedAd.status}</p>
                    <p><strong>Budget:</strong> {formatCurrency(selectedAd.budget)}</p>
                    <p><strong>Duration:</strong> {formatDate(selectedAd.startDate)} to {formatDate(selectedAd.endDate)}</p>
                  </div>
                )}
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => {
                    setShowDeleteModal(false);
                    setSelectedAd(null);
                  }}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn btn-danger"
                  onClick={handleDeleteAd}
                  disabled={actionLoading}
                >
                  {actionLoading ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                      Deleting...
                    </>
                  ) : (
                    'Delete Advertisement'
                  )}
                </button>
              </div>
            </div>
          </div>
          <div className="modal-backdrop fade show"></div>
        </div>
      )}
    </AdminLayout>
  );
}
