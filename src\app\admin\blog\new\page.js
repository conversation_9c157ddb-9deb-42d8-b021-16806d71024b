'use client';

import { useRouter } from 'next/navigation';
import Link from 'next/link';
import AdminLayout from '@/layouts/AdminLayout';
import BlogEditor from '@/components/BlogEditor';
import { useAuth } from '@/hooks/useAuth';

export default function NewBlogPost() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  
  // Redirect if user is not logged in or not an admin
  if (!authLoading && (!user || (user.role !== 'admin' && user.role !== 'super_admin'))) {
    router.push('/login?redirect=/admin/blog/new');
    return null;
  }
  
  return (
    <AdminLayout>
      <div className="admin-blog-new">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h1 className="h3 mb-0 text-gray-800">Create New Blog Post</h1>
          <Link href="/admin/blog" className="btn btn-secondary">
            <i className="fas fa-arrow-left me-2"></i>Back to Blog
          </Link>
        </div>
        
        <div className="card shadow mb-4">
          <div className="card-body">
            <BlogEditor />
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
