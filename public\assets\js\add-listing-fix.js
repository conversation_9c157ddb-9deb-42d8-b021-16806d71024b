// <PERSON>ript to force the Add Listing button to be green
document.addEventListener('DOMContentLoaded', function() {
  // Function to apply styles to Add Listing buttons
  function fixAddListingButtons() {
    // Find all Add Listing buttons by text content
    const allLinks = document.querySelectorAll('a');
    allLinks.forEach(link => {
      if (link.textContent.trim() === 'Add Listing' ||
          link.innerHTML.includes('Add Listing') ||
          (link.parentElement && link.parentElement.classList.contains('add-listing'))) {

        // Add our custom class instead of inline styles
        link.classList.add('add-listing-button');

        // Remove any Bootstrap classes that might be causing issues
        link.classList.remove('btn-primary');
        link.classList.remove('btn-secondary');
        link.classList.remove('btn-info');
        link.classList.remove('btn-success');
        link.classList.remove('btn-danger');
        link.classList.remove('btn-warning');
        link.classList.remove('btn-light');
        link.classList.remove('btn-dark');
        link.classList.remove('btn-outline-primary');
        link.classList.remove('btn-outline-secondary');
        link.classList.remove('btn-outline-info');
        link.classList.remove('btn-outline-success');
        link.classList.remove('btn-outline-danger');
        link.classList.remove('btn-outline-warning');
        link.classList.remove('btn-outline-light');
        link.classList.remove('btn-outline-dark');
        link.classList.remove('btn-icon');
      }
    });
  }

  // Run immediately
  fixAddListingButtons();

  // Also run after a short delay to catch any dynamically loaded buttons
  setTimeout(fixAddListingButtons, 500);
  setTimeout(fixAddListingButtons, 1000);
});
